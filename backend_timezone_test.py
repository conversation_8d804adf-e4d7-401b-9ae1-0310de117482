#!/usr/bin/env python3
"""
后端时区修复测试脚本
用于验证PostgreSQL时区设置是否正确
"""

import psycopg2
from psycopg2.extras import RealDictCursor
from datetime import datetime
import json

# 数据库连接配置
DB_CONFIG = {
    'host': '*************',
    'port': 5432,
    'database': 'postgres',
    'user': 'postgres',
    'password': 'e82e0c77a0eb'
}

def get_db_connection():
    """获取数据库连接，并设置时区为北京时间"""
    conn = psycopg2.connect(**DB_CONFIG)
    # 设置连接的时区为北京时间
    cursor = conn.cursor()
    cursor.execute("SET timezone = 'Asia/Shanghai'")
    conn.commit()
    return conn

def test_timezone_settings():
    """测试时区设置"""
    print("=" * 60)
    print("PostgreSQL 时区设置测试")
    print("=" * 60)
    
    try:
        conn = get_db_connection()
        cursor = conn.cursor()
        
        # 1. 检查当前时区设置
        cursor.execute("SHOW timezone")
        timezone_info = cursor.fetchone()
        print(f"✅ 数据库时区设置: {timezone_info[0]}")
        
        # 2. 显示各种时间
        cursor.execute("""
            SELECT 
                CURRENT_TIMESTAMP as server_time,
                CURRENT_TIMESTAMP AT TIME ZONE 'UTC' as utc_time,
                CURRENT_TIMESTAMP AT TIME ZONE 'Asia/Shanghai' as beijing_time,
                NOW() as now_time
        """)
        time_info = cursor.fetchone()
        
        print(f"🕐 服务器时间: {time_info[0]}")
        print(f"🌍 UTC时间: {time_info[1]}")
        print(f"🇨🇳 北京时间: {time_info[2]}")
        print(f"⏰ NOW()时间: {time_info[3]}")
        
        # 3. 测试插入和查询时间
        print("\n" + "=" * 40)
        print("时间插入和查询测试")
        print("=" * 40)
        
        # 创建测试表
        cursor.execute("""
            CREATE TEMP TABLE timezone_test (
                id SERIAL PRIMARY KEY,
                test_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                beijing_time TIMESTAMP DEFAULT (CURRENT_TIMESTAMP AT TIME ZONE 'Asia/Shanghai')
            )
        """)
        
        # 插入测试数据
        cursor.execute("INSERT INTO timezone_test DEFAULT VALUES")
        
        # 查询测试数据
        cursor.execute("""
            SELECT 
                id,
                test_time,
                beijing_time,
                test_time AT TIME ZONE 'Asia/Shanghai' as converted_beijing_time
            FROM timezone_test
        """)
        
        test_data = cursor.fetchone()
        print(f"📝 插入的默认时间: {test_data[1]}")
        print(f"📝 插入的北京时间: {test_data[2]}")
        print(f"📝 转换后的北京时间: {test_data[3]}")
        
        # 4. 模拟实际应用场景
        print("\n" + "=" * 40)
        print("实际应用场景测试")
        print("=" * 40)
        
        # 模拟插入物料请求
        cursor.execute("""
            SELECT 
                CURRENT_TIMESTAMP AT TIME ZONE 'Asia/Shanghai' as request_time_beijing,
                (CURRENT_TIMESTAMP AT TIME ZONE 'Asia/Shanghai')::timestamp as formatted_time
        """)
        
        app_test = cursor.fetchone()
        print(f"🔧 应用插入时间(北京): {app_test[0]}")
        print(f"🔧 格式化后的时间: {app_test[1]}")
        
        conn.close()
        print("\n✅ 时区测试完成！")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

def test_material_requests_timezone():
    """测试物料请求表的时区处理"""
    print("\n" + "=" * 60)
    print("物料请求表时区测试")
    print("=" * 60)
    
    try:
        conn = get_db_connection()
        cursor = conn.cursor(cursor_factory=RealDictCursor)
        
        # 查询最近的几条物料请求记录
        cursor.execute("""
            SELECT 
                id,
                request_time,
                request_time AT TIME ZONE 'Asia/Shanghai' as beijing_request_time,
                (request_time AT TIME ZONE 'Asia/Shanghai')::timestamp as formatted_beijing_time,
                dispatch_time,
                (dispatch_time AT TIME ZONE 'Asia/Shanghai')::timestamp as formatted_dispatch_time
            FROM maf_pulling.material_requests 
            ORDER BY request_time DESC 
            LIMIT 5
        """)
        
        requests = cursor.fetchall()
        
        if requests:
            print("📋 最近的物料请求记录:")
            for req in requests:
                print(f"  ID: {req['id']}")
                print(f"    原始请求时间: {req['request_time']}")
                print(f"    北京请求时间: {req['beijing_request_time']}")
                print(f"    格式化北京时间: {req['formatted_beijing_time']}")
                if req['dispatch_time']:
                    print(f"    格式化发料时间: {req['formatted_dispatch_time']}")
                print()
        else:
            print("📋 暂无物料请求记录")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ 物料请求测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_timezone_settings()
    test_material_requests_timezone()
    
    print("\n" + "=" * 60)
    print("测试总结")
    print("=" * 60)
    print("1. 数据库连接时设置时区为 Asia/Shanghai")
    print("2. 插入时间使用 CURRENT_TIMESTAMP AT TIME ZONE 'Asia/Shanghai'")
    print("3. 查询时间使用 (time_field AT TIME ZONE 'Asia/Shanghai')::timestamp")
    print("4. 前端直接使用 new Date() 解析返回的时间字符串")
    print("=" * 60)
