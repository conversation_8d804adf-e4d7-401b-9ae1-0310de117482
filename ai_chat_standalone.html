<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI聊天助手 - 独立版</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/themes/prism.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/themes/prism-tomorrow.min.css" rel="stylesheet">
    <script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/components/prism-core.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/plugins/autoloader/prism-autoloader.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/marked/9.1.6/marked.min.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            height: 100vh;
            display: flex;
            flex-direction: column;
        }

        .header {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            padding: 15px 20px;
            border-bottom: 1px solid rgba(255, 255, 255, 0.2);
        }

        .header h1 {
            color: white;
            font-size: 24px;
            margin-bottom: 10px;
        }

        .config-panel {
            display: flex;
            gap: 15px;
            align-items: center;
            flex-wrap: wrap;
        }

        .config-group {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .config-group label {
            color: white;
            font-size: 14px;
            font-weight: 500;
        }

        .config-group select, .config-group input {
            padding: 6px 12px;
            border: 1px solid rgba(255, 255, 255, 0.3);
            border-radius: 6px;
            background: rgba(255, 255, 255, 0.1);
            color: white;
            font-size: 14px;
        }

        .config-group select option {
            background: #333;
            color: white;
        }

        .config-group input::placeholder {
            color: rgba(255, 255, 255, 0.7);
        }

        .api-config {
            width: 100%;
            margin-top: 10px;
        }

        .api-config input {
            width: 100%;
            padding: 8px 12px;
            border: 1px solid rgba(255, 255, 255, 0.3);
            border-radius: 6px;
            background: rgba(255, 255, 255, 0.1);
            color: white;
            font-size: 14px;
        }

        .chat-container {
            flex: 1;
            display: flex;
            flex-direction: column;
            max-width: 1200px;
            margin: 0 auto;
            width: 100%;
            padding: 20px;
            overflow: hidden;
        }

        .messages {
            flex: 1;
            overflow-y: auto;
            padding: 20px;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 12px;
            margin-bottom: 20px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        }

        .message {
            margin-bottom: 20px;
            display: flex;
            gap: 12px;
        }

        .message.user {
            flex-direction: row-reverse;
        }

        .message-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            color: white;
            font-size: 14px;
            flex-shrink: 0;
        }

        .message.user .message-avatar {
            background: linear-gradient(135deg, #667eea, #764ba2);
        }

        .message.assistant .message-avatar {
            background: linear-gradient(135deg, #f093fb, #f5576c);
        }

        .message-content {
            flex: 1;
            max-width: 70%;
        }

        .message-bubble {
            padding: 12px 16px;
            border-radius: 18px;
            word-wrap: break-word;
            line-height: 1.5;
        }

        .message.user .message-bubble {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            margin-left: auto;
        }

        .message.assistant .message-bubble {
            background: #f8f9fa;
            color: #333;
            border: 1px solid #e9ecef;
        }

        .message-bubble img {
            max-width: 100%;
            border-radius: 8px;
            margin: 8px 0;
        }

        .input-area {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 12px;
            padding: 20px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        }

        .input-container {
            display: flex;
            gap: 12px;
            align-items: flex-end;
        }

        .input-wrapper {
            flex: 1;
            position: relative;
        }

        #messageInput {
            width: 100%;
            min-height: 50px;
            max-height: 150px;
            padding: 12px 50px 12px 16px;
            border: 2px solid #e9ecef;
            border-radius: 25px;
            font-size: 16px;
            resize: none;
            outline: none;
            transition: border-color 0.3s;
        }

        #messageInput:focus {
            border-color: #667eea;
        }

        .file-input-btn {
            position: absolute;
            right: 12px;
            top: 50%;
            transform: translateY(-50%);
            background: none;
            border: none;
            cursor: pointer;
            color: #666;
            font-size: 20px;
            padding: 4px;
            border-radius: 4px;
            transition: color 0.3s;
        }

        .file-input-btn:hover {
            color: #667eea;
        }

        #fileInput {
            display: none;
        }

        .send-btn {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            border: none;
            border-radius: 50%;
            width: 50px;
            height: 50px;
            cursor: pointer;
            font-size: 20px;
            transition: transform 0.2s;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .send-btn:hover {
            transform: scale(1.05);
        }

        .send-btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }

        .image-preview {
            display: flex;
            gap: 10px;
            margin-top: 10px;
            flex-wrap: wrap;
        }

        .image-preview-item {
            position: relative;
            display: inline-block;
        }

        .image-preview img {
            width: 80px;
            height: 80px;
            object-fit: cover;
            border-radius: 8px;
            border: 2px solid #e9ecef;
        }

        .image-preview-remove {
            position: absolute;
            top: -8px;
            right: -8px;
            background: #ff4757;
            color: white;
            border: none;
            border-radius: 50%;
            width: 20px;
            height: 20px;
            cursor: pointer;
            font-size: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .typing-indicator {
            display: none;
            align-items: center;
            gap: 8px;
            color: #666;
            font-style: italic;
            margin-bottom: 10px;
        }

        .typing-dots {
            display: flex;
            gap: 4px;
        }

        .typing-dots span {
            width: 6px;
            height: 6px;
            background: #666;
            border-radius: 50%;
            animation: typing 1.4s infinite;
        }

        .typing-dots span:nth-child(2) {
            animation-delay: 0.2s;
        }

        .typing-dots span:nth-child(3) {
            animation-delay: 0.4s;
        }

        @keyframes typing {
            0%, 60%, 100% {
                transform: translateY(0);
            }
            30% {
                transform: translateY(-10px);
            }
        }

        /* Markdown样式 */
        .message-bubble h1, .message-bubble h2, .message-bubble h3,
        .message-bubble h4, .message-bubble h5, .message-bubble h6 {
            margin: 16px 0 8px 0;
            color: #333;
        }

        .message-bubble p {
            margin: 8px 0;
        }

        .message-bubble ul, .message-bubble ol {
            margin: 8px 0;
            padding-left: 20px;
        }

        .message-bubble blockquote {
            border-left: 4px solid #667eea;
            padding-left: 16px;
            margin: 16px 0;
            color: #666;
            font-style: italic;
        }

        .message-bubble code {
            background: #f1f3f4;
            padding: 2px 6px;
            border-radius: 4px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
        }

        .message-bubble pre {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            padding: 16px;
            overflow-x: auto;
            margin: 16px 0;
        }

        .message-bubble pre code {
            background: none;
            padding: 0;
        }

        .message-bubble table {
            border-collapse: collapse;
            width: 100%;
            margin: 16px 0;
        }

        .message-bubble th, .message-bubble td {
            border: 1px solid #ddd;
            padding: 8px 12px;
            text-align: left;
        }

        .message-bubble th {
            background: #f8f9fa;
            font-weight: bold;
        }

        @media (max-width: 768px) {
            .config-panel {
                flex-direction: column;
                align-items: stretch;
            }

            .config-group {
                justify-content: space-between;
            }

            .chat-container {
                padding: 10px;
            }

            .message-content {
                max-width: 85%;
            }

            .input-container {
                flex-direction: column;
                gap: 10px;
            }

            .send-btn {
                align-self: flex-end;
            }
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🤖 AI聊天助手 - 独立版</h1>
        <div class="config-panel">
            <div class="config-group">
                <label for="modelSelect">模型:</label>
                <select id="modelSelect">
                    <option value="DeepSeek-V3">DeepSeek-V3</option>
                    <option value="Doubao-1.5-vision-pro">Doubao-1.5-vision-pro</option>
                    <option value="gpt-4">GPT-4</option>
                    <option value="gpt-3.5-turbo">GPT-3.5-Turbo</option>
                </select>
            </div>
            <div class="config-group">
                <label for="temperatureInput">温度:</label>
                <input type="number" id="temperatureInput" min="0" max="2" step="0.1" value="0.7" style="width: 80px;">
            </div>
            <div class="config-group">
                <label for="maxTokensInput">最大Token:</label>
                <input type="number" id="maxTokensInput" min="1" max="8192" value="2048" style="width: 100px;">
            </div>
            <div class="api-config">
                <input type="text" id="apiUrlInput" placeholder="API地址 (例: http://10.155.96.102:8013/v3/chat/completions)"
                       value="http://10.155.96.102:8013/v3/chat/completions">
            </div>
            <div class="api-config">
                <input type="text" id="apiKeyInput" placeholder="API密钥"
                       value="3e0d356f-fe37-bc9d-e101-672f6b19f29f">
            </div>
        </div>
    </div>

    <div class="chat-container">
        <div class="messages" id="messages">
            <div class="message assistant">
                <div class="message-avatar">AI</div>
                <div class="message-content">
                    <div class="message-bubble">
                        <p>你好！我是AI助手，这是独立版本，可以直接配置API地址和密钥。你可以：</p>
                        <ul>
                            <li>在上方配置API地址和密钥</li>
                            <li>选择不同的AI模型</li>
                            <li>直接输入文字与我对话</li>
                            <li>上传图片让我分析</li>
                        </ul>
                        <p>有什么我可以帮助你的吗？</p>
                    </div>
                </div>
            </div>
        </div>

        <div class="typing-indicator" id="typingIndicator">
            <span>AI正在思考</span>
            <div class="typing-dots">
                <span></span>
                <span></span>
                <span></span>
            </div>
        </div>

        <div class="input-area">
            <div class="image-preview" id="imagePreview"></div>
            <div class="input-container">
                <div class="input-wrapper">
                    <textarea id="messageInput" placeholder="输入你的消息..." rows="1"></textarea>
                    <button class="file-input-btn" onclick="document.getElementById('fileInput').click()">
                        📎
                    </button>
                    <input type="file" id="fileInput" accept="image/*" multiple>
                </div>
                <button class="send-btn" id="sendBtn" onclick="sendMessage()">
                    ➤
                </button>
            </div>
        </div>
    </div>
