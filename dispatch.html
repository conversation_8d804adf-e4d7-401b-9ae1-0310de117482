<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>发料清单管理</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .container {
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
            max-width: 1200px;
            margin: 0 auto;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .header h1 {
            font-size: 28px;
            margin-bottom: 10px;
        }
        
        .header p {
            font-size: 14px;
            opacity: 0.9;
        }
        
        .controls {
            padding: 20px 30px;
            background: #f8f9fa;
            border-bottom: 1px solid #e9ecef;
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
            gap: 15px;
        }

        .filter-info {
            background: #e3f2fd;
            color: #1565c0;
            padding: 10px 15px;
            border-radius: 6px;
            font-size: 14px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .clear-filter-btn {
            background: #1976d2;
            color: white;
            border: none;
            padding: 4px 8px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 12px;
        }

        .clear-filter-btn:hover {
            background: #1565c0;
        }

        .control-buttons {
            display: flex;
            gap: 10px;
        }

        .refresh-btn {
            background: #28a745;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            transition: background 0.3s;
        }

        .refresh-btn:hover {
            background: #218838;
        }
        
        .content {
            padding: 30px;
        }
        
        .requests-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }
        
        .requests-table th,
        .requests-table td {
            padding: 15px;
            text-align: left;
            border-bottom: 1px solid #e9ecef;
        }
        
        .requests-table th {
            background: #f8f9fa;
            font-weight: 600;
            color: #495057;
        }
        
        .requests-table tr:hover {
            background: #f8f9fa;
        }
        
        .dispatch-btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 12px;
            transition: background 0.3s;
        }
        
        .dispatch-btn:hover {
            background: #0056b3;
        }
        
        .dispatch-btn:disabled {
            background: #6c757d;
            cursor: not-allowed;
        }
        
        .empty-state {
            text-align: center;
            padding: 60px 20px;
            color: #6c757d;
        }
        
        .empty-state h3 {
            margin-bottom: 10px;
            font-size: 20px;
        }
        
        .empty-state p {
            font-size: 14px;
        }
        
        .loading {
            text-align: center;
            padding: 40px;
            color: #6c757d;
        }
        
        .message {
            padding: 12px;
            border-radius: 6px;
            margin-bottom: 20px;
            text-align: center;
        }
        
        .message.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .message.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .status-badge {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 500;
        }
        
        .status-pending {
            background: #fff3cd;
            color: #856404;
        }
        
        .status-completed {
            background: #d4edda;
            color: #155724;
        }

        /* 已完成的请求样式 */
        .completed-request {
            text-decoration: line-through;
            opacity: 0.7;
        }

        /* 延迟请求样式 */
        .delayed-request {
            background-color: #ffebee !important;
            border-left: 4px solid #f44336;
        }

        .delayed-request td {
            color: #d32f2f;
            font-weight: 500;
        }

        /* 超时请求样式 - 更严重的超时 */
        .timeout-request {
            background-color: #ffcdd2 !important;
            border-left: 6px solid #d32f2f;
            animation: pulse-red 2s infinite;
        }

        .timeout-request td {
            color: #b71c1c !important;
            font-weight: 600 !important;
        }

        /* 脉冲动画提醒 */
        @keyframes pulse-red {
            0% { background-color: #ffcdd2; }
            50% { background-color: #ffebee; }
            100% { background-color: #ffcdd2; }
        }

        .delay-indicator {
            background: #f44336;
            color: white;
            padding: 2px 6px;
            border-radius: 10px;
            font-size: 11px;
            font-weight: bold;
            margin-left: 8px;
        }

        .timeout-indicator {
            background: #d32f2f;
            color: white;
            padding: 2px 6px;
            border-radius: 10px;
            font-size: 11px;
            font-weight: bold;
            margin-left: 8px;
            animation: blink 1s infinite;
        }

        @keyframes blink {
            0%, 50% { opacity: 1; }
            51%, 100% { opacity: 0.5; }
        }

        .time-elapsed {
            font-size: 11px;
            color: #666;
            display: block;
            margin-top: 2px;
        }

        .time-elapsed.warning {
            color: #f57c00;
            font-weight: 500;
        }

        .time-elapsed.danger {
            color: #d32f2f;
            font-weight: 600;
        }
        
        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.5);
        }
        
        .modal-content {
            background-color: white;
            margin: 15% auto;
            padding: 30px;
            border-radius: 10px;
            width: 400px;
            text-align: center;
        }
        
        .modal-buttons {
            margin-top: 20px;
        }
        
        .modal-btn {
            padding: 10px 20px;
            margin: 0 10px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
        }
        
        .modal-btn.confirm {
            background: #dc3545;
            color: white;
        }
        
        .modal-btn.cancel {
            background: #6c757d;
            color: white;
        }

        /* 右下角配置角标 */
        .config-badge {
            position: fixed;
            bottom: 20px;
            right: 20px;
            z-index: 1000;
        }

        .config-toggle {
            background: #667eea;
            color: white;
            border: none;
            width: 60px;
            height: 60px;
            border-radius: 50%;
            cursor: pointer;
            font-size: 24px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            transition: all 0.3s;
        }

        .config-toggle:hover {
            background: #5a6fd8;
            transform: scale(1.1);
        }

        .config-panel {
            position: absolute;
            bottom: 70px;
            right: 0;
            background: white;
            border-radius: 10px;
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
            padding: 20px;
            width: 300px;
            display: none;
        }

        .config-panel.show {
            display: block;
        }

        .config-group {
            margin-bottom: 15px;
        }

        .config-group:last-child {
            margin-bottom: 0;
        }

        .config-group label {
            display: block;
            margin-bottom: 5px;
            font-size: 14px;
            color: #495057;
            font-weight: 500;
        }

        .config-group input,
        .config-group select {
            width: 100%;
            padding: 8px 12px;
            border: 1px solid #ced4da;
            border-radius: 4px;
            font-size: 14px;
        }

        .config-group input:focus,
        .config-group select:focus {
            outline: none;
            border-color: #667eea;
        }

        .config-actions {
            display: flex;
            gap: 10px;
            margin-top: 15px;
        }

        .config-btn {
            flex: 1;
            padding: 8px 16px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            transition: background 0.3s;
        }

        .config-btn.primary {
            background: #667eea;
            color: white;
        }

        .config-btn.primary:hover {
            background: #5a6fd8;
        }

        .config-btn.secondary {
            background: #6c757d;
            color: white;
        }

        .config-btn.secondary:hover {
            background: #5a6268;
        }

        @media (max-width: 768px) {
            .container {
                margin: 0;
                border-radius: 0;
            }

            .controls {
                flex-direction: column;
                gap: 15px;
            }

            .requests-table {
                font-size: 12px;
            }

            .requests-table th,
            .requests-table td {
                padding: 10px 8px;
            }

            .config-panel {
                width: 280px;
                right: -10px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>发料清单管理</h1>
            <p>查看和处理所有待发料的物料请求</p>
        </div>
        
        <div class="controls">
            <div id="filterInfo" class="filter-info" style="display: none;">
                <span id="filterText"></span>
                <button class="clear-filter-btn" onclick="clearFilter()">显示所有</button>
            </div>
            <div class="control-buttons">
                <button class="refresh-btn" onclick="loadRequests()">刷新列表</button>
            </div>
        </div>
        
        <div class="content">
            <div id="message" class="message" style="display: none;"></div>
            
            <div id="loading" class="loading">正在加载发料清单...</div>
            
            <div id="requestsContainer" style="display: none;">
                <table class="requests-table">
                    <thead>
                        <tr>
                            <th>请求ID</th>
                            <th>生产线体</th>
                            <th>物料编码</th>
                            <th>物料名称</th>
                            <th>数量</th>
                            <th>请求时间</th>
                            <th>状态</th>
                            <th>操作/发料时间</th>
                        </tr>
                    </thead>
                    <tbody id="requestsTableBody">
                    </tbody>
                </table>
            </div>
            
            <div id="emptyState" class="empty-state" style="display: none;">
                <h3>暂无待发料请求</h3>
                <p>当前没有需要处理的物料请求</p>
            </div>
        </div>
    </div>

    <!-- 右下角配置角标 -->
    <div class="config-badge">
        <button class="config-toggle" onclick="toggleConfig()">⚙️</button>
        <div class="config-panel" id="configPanel">
            <div class="config-group">
                <label for="serverUrl">后端服务器地址：</label>
                <input type="text" id="serverUrl" value="http://localhost:8000" placeholder="http://localhost:8000">
            </div>
            <div class="config-group">
                <label for="productionLineSelect">筛选生产线体：</label>
                <select id="productionLineSelect">
                    <option value="">显示所有线体的物料</option>
                </select>
            </div>
            <div class="config-group">
                <label for="dateSelect">查看日期：</label>
                <input type="date" id="dateSelect">
            </div>
            <div class="config-group">
                <label for="timeoutHours">超时提醒阈值（小时）：</label>
                <input type="number" id="timeoutHours" value="2" min="1" max="24" placeholder="2">
                <small style="color: #6c757d; font-size: 12px;">超过此时间未发料将显示红色警告</small>
            </div>
            <div class="config-actions">
                <button class="config-btn primary" onclick="applyConfig()">应用</button>
                <button class="config-btn secondary" onclick="closeConfig()">关闭</button>
            </div>
        </div>
    </div>

    <!-- 确认发料模态框 -->
    <div id="confirmModal" class="modal">
        <div class="modal-content">
            <h3>确认发料</h3>
            <p id="confirmText">确定要发放这个物料吗？</p>
            <div class="modal-buttons">
                <button class="modal-btn confirm" onclick="confirmDispatch()">确认发料</button>
                <button class="modal-btn cancel" onclick="closeModal()">取消</button>
            </div>
        </div>
    </div>

    <script>
        let currentRequestId = null;
        let currentProductionLineId = null;
        let currentDate = null;
        let timeoutHours = 2; // 默认超时阈值为2小时

        // 获取服务器地址
        function getServerUrl() {
            return document.getElementById('serverUrl').value.trim() || 'http://localhost:8000';
        }

        // 初始化日期为今天
        function initDate() {
            const today = new Date();
            const dateString = today.toISOString().split('T')[0];
            document.getElementById('dateSelect').value = dateString;
            currentDate = dateString;
        }

        // 页面加载时获取发料清单
        document.addEventListener('DOMContentLoaded', function() {
            initDate();
            loadSavedSettings();
            loadProductionLines();
            loadRequests();
            // 每30秒自动刷新一次
            setInterval(loadRequests, 30000);
        });

        // 加载保存的设置
        function loadSavedSettings() {
            const savedTimeoutHours = localStorage.getItem('timeoutHours');
            if (savedTimeoutHours) {
                timeoutHours = parseInt(savedTimeoutHours);
                document.getElementById('timeoutHours').value = timeoutHours;
            }
        }
        
        // 加载生产线体列表
        function loadProductionLines() {
            const serverUrl = getServerUrl();
            fetch(`${serverUrl}/api/production-lines`)
                .then(response => {
                    if (!response.ok) {
                        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                    }
                    return response.json();
                })
                .then(lines => {
                    const productionLineSelect = document.getElementById('productionLineSelect');
                    productionLineSelect.innerHTML = '<option value="">显示所有线体的物料</option>';
                    lines.forEach(line => {
                        const option = document.createElement('option');
                        option.value = line.id;
                        option.textContent = `${line.name} - ${line.description}`;
                        productionLineSelect.appendChild(option);
                    });
                })
                .catch(error => {
                    console.error('加载生产线体失败:', error);
                    showMessage(`加载生产线体失败: ${error.message}`, 'error');
                });
        }

        // 加载发料请求列表
        function loadRequests() {
            document.getElementById('loading').style.display = 'block';
            document.getElementById('requestsContainer').style.display = 'none';
            document.getElementById('emptyState').style.display = 'none';

            const serverUrl = getServerUrl();
            let apiUrl = `${serverUrl}/api/requests/filtered`;

            // 构建查询参数
            const params = new URLSearchParams();
            if (currentProductionLineId) {
                params.append('production_line_id', currentProductionLineId);
            }
            if (currentDate) {
                params.append('date', currentDate);
            }

            if (params.toString()) {
                apiUrl += `?${params.toString()}`;
            }

            fetch(apiUrl)
                .then(response => {
                    if (!response.ok) {
                        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                    }
                    return response.json();
                })
                .then(requests => {
                    document.getElementById('loading').style.display = 'none';
                    updateFilterInfo();

                    if (requests.length === 0) {
                        document.getElementById('emptyState').style.display = 'block';
                    } else {
                        displayRequests(requests);
                        document.getElementById('requestsContainer').style.display = 'block';
                    }
                })
                .catch(error => {
                    document.getElementById('loading').style.display = 'none';
                    console.error('加载发料清单失败:', error);
                    showMessage(`加载发料清单失败: ${error.message}`, 'error');
                });
        }

        // 检查请求是否延迟或超时
        function getRequestTimeStatus(requestTime, status) {
            if (status === 'completed') return { type: 'completed', hoursDiff: 0 };

            const requestDate = new Date(requestTime);
            const now = new Date();
            const hoursDiff = (now - requestDate) / (1000 * 60 * 60);

            if (hoursDiff > timeoutHours) {
                return { type: 'timeout', hoursDiff: hoursDiff };
            } else if (hoursDiff > 1) {
                return { type: 'delayed', hoursDiff: hoursDiff };
            } else {
                return { type: 'normal', hoursDiff: hoursDiff };
            }
        }

        // 格式化经过的时间
        function formatElapsedTime(hoursDiff) {
            if (hoursDiff < 1) {
                const minutes = Math.floor(hoursDiff * 60);
                return `${minutes}分钟前`;
            } else if (hoursDiff < 24) {
                const hours = Math.floor(hoursDiff);
                const minutes = Math.floor((hoursDiff - hours) * 60);
                return `${hours}小时${minutes > 0 ? minutes + '分钟' : ''}前`;
            } else {
                const days = Math.floor(hoursDiff / 24);
                const hours = Math.floor(hoursDiff % 24);
                return `${days}天${hours > 0 ? hours + '小时' : ''}前`;
            }
        }

        // 显示请求列表
        function displayRequests(requests) {
            const tbody = document.getElementById('requestsTableBody');
            tbody.innerHTML = '';

            requests.forEach(request => {

                const row = document.createElement('tr');
                const isCompleted = request.status === 'completed';
                const timeStatus = getRequestTimeStatus(request.request_time, request.status);
                console.log(request.request_time);
                // 设置行的样式类
                if (isCompleted) {
                    row.className = 'completed-request';
                } else if (timeStatus.type === 'timeout') {
                    row.className = 'timeout-request';
                } else if (timeStatus.type === 'delayed') {
                    row.className = 'delayed-request';
                }

                // 构建状态显示
                let statusDisplay = `<span class="status-badge status-${request.status}">${getStatusText(request.status)}</span>`;
                if (timeStatus.type === 'timeout' && !isCompleted) {
                    statusDisplay += '<span class="timeout-indicator">超时</span>';
                } else if (timeStatus.type === 'delayed' && !isCompleted) {
                    statusDisplay += '<span class="delay-indicator">延迟</span>';
                }

                // 构建请求时间显示（包含经过时间）
                let requestTimeDisplay = formatDateTime(request.request_time);
                if (!isCompleted && timeStatus.hoursDiff > 0.5) { // 超过30分钟显示经过时间
                    const elapsedTime = formatElapsedTime(timeStatus.hoursDiff);
                    let elapsedClass = 'time-elapsed';
                    if (timeStatus.type === 'timeout') {
                        elapsedClass += ' danger';
                    } else if (timeStatus.type === 'delayed') {
                        elapsedClass += ' warning';
                    }
                    requestTimeDisplay += `<span class="${elapsedClass}">(${elapsedTime})</span>`;
                }

                // 构建操作按钮
                let actionButton = '';
                if (!isCompleted) {
                    actionButton = `
                        <button class="dispatch-btn" onclick="showDispatchModal(${request.id}, '${request.material_name}', '${request.line_name}')">
                            发料
                        </button>
                    `;
                } else {
                    actionButton = `
                        <span style="color: #6c757d; font-size: 12px;">
                            ${formatDateTime(request.dispatch_time)}
                        </span>
                    `;
                }

                row.innerHTML = `
                    <td>#${request.id}</td>
                    <td>${request.line_name}</td>
                    <td>${request.material_code}</td>
                    <td>${request.material_name}</td>
                    <td>${request.quantity} ${request.unit}</td>
                    <td>${requestTimeDisplay}</td>
<!--                    <td>${request.request_time}</td>-->
                    <td>${statusDisplay}</td>
                    <td>${actionButton}</td>
                `;
                tbody.appendChild(row);
            });
        }

        // 显示发料确认模态框
        function showDispatchModal(requestId, materialName, lineName) {
            currentRequestId = requestId;
            document.getElementById('confirmText').textContent =
                `确定要向 ${lineName} 发放 ${materialName} 吗？`;
            document.getElementById('confirmModal').style.display = 'block';
        }

        // 关闭模态框
        function closeModal() {
            document.getElementById('confirmModal').style.display = 'none';
            currentRequestId = null;
        }

        // 确认发料
        function confirmDispatch() {
            if (!currentRequestId) return;

            const serverUrl = getServerUrl();
            fetch(`${serverUrl}/api/dispatch/${currentRequestId}`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                }
            })
            .then(response => {
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                return response.json();
            })
            .then(data => {
                if (data.success) {
                    showMessage('发料完成！', 'success');
                    loadRequests(); // 重新加载列表
                } else {
                    showMessage(data.message || '发料失败', 'error');
                }
            })
            .catch(error => {
                console.error('发料失败:', error);
                showMessage(`发料失败: ${error.message}`, 'error');
            })
            .finally(() => {
                closeModal();
            });
        }

        // 格式化日期时间
        function formatDateTime(dateTimeStr) {
            const date = new Date(dateTimeStr);
            return date.toLocaleString('zh-CN', {
                year: 'numeric',
                month: '2-digit',
                day: '2-digit',
                hour: '2-digit',
                minute: '2-digit',
                timeZone: 'UTC' // 强制使用 UTC 时间
            });
        }

        // 获取状态文本
        function getStatusText(status) {
            const statusMap = {
                'pending': '待发料',
                'completed': '已完成'
            };
            return statusMap[status] || status;
        }

        // 显示消息
        function showMessage(text, type) {
            const messageDiv = document.getElementById('message');
            messageDiv.textContent = text;
            messageDiv.className = `message ${type}`;
            messageDiv.style.display = 'block';

            setTimeout(() => {
                messageDiv.style.display = 'none';
            }, 5000);
        }

        // 更新筛选信息显示
        function updateFilterInfo() {
            const filterInfo = document.getElementById('filterInfo');
            const filterText = document.getElementById('filterText');

            let filterParts = [];

            if (currentProductionLineId) {
                const productionLineSelect = document.getElementById('productionLineSelect');
                const selectedOption = productionLineSelect.options[productionLineSelect.selectedIndex];
                if (selectedOption && selectedOption.value) {
                    filterParts.push(`生产线体：${selectedOption.textContent}`);
                }
            }

            if (currentDate) {
                const dateObj = new Date(currentDate);
                const dateString = dateObj.toLocaleDateString('zh-CN');
                filterParts.push(`日期：${dateString}`);
            }

            if (filterParts.length > 0) {
                filterText.textContent = `当前筛选：${filterParts.join('，')}`;
                filterInfo.style.display = 'flex';
            } else {
                filterInfo.style.display = 'none';
            }
        }

        // 清除筛选
        function clearFilter() {
            currentProductionLineId = null;
            currentDate = null;
            document.getElementById('productionLineSelect').value = '';
            // 重置日期为今天
            initDate();
            updateFilterInfo();
            loadRequests();
        }

        // 配置面板控制
        function toggleConfig() {
            const panel = document.getElementById('configPanel');
            panel.classList.toggle('show');
        }

        function closeConfig() {
            const panel = document.getElementById('configPanel');
            panel.classList.remove('show');
        }

        // 应用配置
        function applyConfig() {
            const productionLineId = document.getElementById('productionLineSelect').value;
            const selectedDate = document.getElementById('dateSelect').value;
            const newTimeoutHours = parseInt(document.getElementById('timeoutHours').value) || 2;

            currentProductionLineId = productionLineId || null;
            currentDate = selectedDate;
            timeoutHours = newTimeoutHours;

            // 保存超时设置到本地存储
            localStorage.setItem('timeoutHours', timeoutHours);

            loadRequests();
            closeConfig();
        }

        // 点击模态框外部关闭
        window.onclick = function(event) {
            const modal = document.getElementById('confirmModal');
            if (event.target === modal) {
                closeModal();
            }
        }

        // 点击配置面板外部关闭
        document.addEventListener('click', function(event) {
            const configBadge = document.querySelector('.config-badge');
            const configPanel = document.getElementById('configPanel');

            if (!configBadge.contains(event.target) && configPanel.classList.contains('show')) {
                closeConfig();
            }
        });
    </script>
</body>
</html>
