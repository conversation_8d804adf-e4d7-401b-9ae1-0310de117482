# 主数据配置和管理系统

## 系统概述

基于您现有的后端数据库表结构，我为您设计并实现了一个完整的主数据配置和管理页面。该系统提供了对以下核心数据的全面管理：

### 核心数据表
1. **生产线体表** (`maf_pulling.production_lines`)
2. **物料表** (`maf_pulling.materials`)
3. **线体物料关系表** (`maf_pulling.line_materials`)
4. **物料生产线体关系表** (`maf_pulling.material_production_lines`)
5. **物料请求表** (`maf_pulling.material_requests`)

## 功能特性

### 🏭 生产线体管理
- ✅ 查看所有生产线体
- ✅ 新增生产线体
- ✅ 编辑生产线体信息
- ✅ 删除生产线体（带关联检查）
- ✅ 创建时间记录

### 📦 物料管理
- ✅ 查看所有物料信息
- ✅ 新增物料（物料编码、描述、单位、最小订购量）
- ✅ 编辑物料信息
- ✅ 删除物料（带关联检查）
- ✅ 物料编码唯一性验证

### 🔗 线体物料关系管理
- ✅ 查看线体与物料的使用关系
- ✅ 建立新的线体物料关系
- ✅ 删除关系
- ✅ 关系唯一性约束

### ⚙️ 物料生产关系管理
- ✅ 查看物料与生产线体的生产关系
- ✅ 建立新的物料生产关系
- ✅ 删除关系
- ✅ 关系唯一性约束

### 📊 数据统计概览
- ✅ 实时统计各表数据量
- ✅ 物料请求状态统计
- ✅ 今日请求统计
- ✅ 可视化数据展示

## 页面访问

### 主要页面
- **主数据管理页面**: `http://localhost:8000/admin`
- **API测试页面**: `http://localhost:8000/test_admin.html`
- **叫料页面**: `http://localhost:8000/request.html`
- **发料页面**: `http://localhost:8000/dispatch.html`

### 快速开始

1. **启动后端服务**
   ```bash
   python app.py
   ```

2. **访问管理页面**
   - 打开浏览器访问: `http://localhost:8000/admin`
   - 或者先访问测试页面: `http://localhost:8000/test_admin.html`

3. **测试API功能**
   - 在测试页面中点击各种测试按钮
   - 验证所有CRUD操作是否正常工作

## API接口文档

### 统计数据
- `GET /api/admin/statistics` - 获取数据统计信息

### 生产线体管理
- `GET /api/admin/production-lines` - 获取所有生产线体
- `POST /api/admin/production-lines` - 创建新生产线体
- `PUT /api/admin/production-lines/{line_id}` - 更新生产线体
- `DELETE /api/admin/production-lines/{line_id}` - 删除生产线体

### 物料管理
- `GET /api/admin/materials` - 获取所有物料
- `POST /api/admin/materials` - 创建新物料
- `PUT /api/admin/materials/{material_id}` - 更新物料
- `DELETE /api/admin/materials/{material_id}` - 删除物料

### 线体物料关系管理
- `GET /api/admin/line-materials` - 获取所有线体物料关系
- `POST /api/admin/line-materials` - 创建新关系
- `DELETE /api/admin/line-materials/{relation_id}` - 删除关系

### 物料生产关系管理
- `GET /api/admin/material-production-lines` - 获取所有物料生产关系
- `POST /api/admin/material-production-lines` - 创建新关系
- `DELETE /api/admin/material-production-lines/{relation_id}` - 删除关系

## 界面特性

### 🎨 现代化设计
- 响应式布局，支持移动端
- 渐变色背景和卡片式设计
- 统一的视觉风格
- 直观的操作反馈

### 🛡️ 数据安全
- 删除操作前的确认提示
- 关联数据检查（防止误删有关联的数据）
- 表单验证和错误提示
- 实时数据更新

### 📱 用户体验
- 标签页式导航
- 模态框编辑
- 加载状态提示
- 成功/错误消息提示
- 空状态友好提示

## 数据关系说明

### 线体物料关系 vs 物料生产关系
- **线体物料关系**: 表示某个生产线体**使用**哪些物料（用于叫料页面筛选）
- **物料生产关系**: 表示某个物料可以在哪些生产线体**生产**（用于发料页面筛选）

### 业务逻辑
1. 生产线体可以使用多种物料
2. 物料可以被多个生产线体使用
3. 物料可以在多个生产线体生产
4. 生产线体可以生产多种物料
5. 删除主数据时会检查是否有相关的物料请求记录

## 技术实现

### 后端技术栈
- **Flask**: Web框架
- **PostgreSQL**: 数据库
- **psycopg2**: 数据库连接器
- **Flask-CORS**: 跨域支持

### 前端技术栈
- **HTML5 + CSS3**: 页面结构和样式
- **JavaScript (ES6+)**: 交互逻辑
- **Fetch API**: HTTP请求
- **响应式设计**: 移动端适配

### 数据库设计
- 外键约束确保数据完整性
- 唯一约束防止重复关系
- 时间戳记录创建时间
- 级联删除处理关联数据

## 使用建议

### 数据初始化顺序
1. 首先创建生产线体
2. 然后创建物料
3. 最后建立各种关系

### 最佳实践
1. 定期备份数据库
2. 删除数据前确认无关联记录
3. 使用有意义的物料编码和描述
4. 保持数据的一致性和完整性

## 故障排除

### 常见问题
1. **连接数据库失败**: 检查数据库服务是否启动，连接参数是否正确
2. **删除失败**: 检查是否有关联的物料请求记录
3. **创建重复**: 检查物料编码或关系是否已存在
4. **页面无法访问**: 确认Flask服务是否正常启动

### 调试方法
1. 查看浏览器控制台错误信息
2. 查看Flask服务器日志
3. 使用API测试页面验证后端功能
4. 检查数据库表结构和数据

---

**系统已完成并可以正常使用！** 🎉

您可以通过访问 `http://localhost:8000/admin` 开始使用主数据管理系统。
