# Dispatch页面升级总结

## 最新更新（发料状态显示优化）

### 新增功能
- **已完成请求显示优化**：已完成发料的请求不再隐藏，而是显示但用横线划掉表示已完成
- **延迟请求红色警告**：超过1小时未响应的pending请求用显眼的红色背景和"DELAY"标识表示延迟
- **智能操作列显示**：
  - 未完成请求：显示"发料"按钮
  - 已完成请求：显示发料完成时间
- **表头优化**：将"操作"列改为"操作/发料时间"更准确地反映列内容

## 完成的工作

### 1. 数据库设计
- **新建表**: `maf_pulling.material_production_lines`
  - 用途：存储物料与生产线体的关系，表示哪个物料可以在哪个生产线体生产
  - 字段：
    - `id`: 主键
    - `material_id`: 物料ID（外键）
    - `production_line_id`: 生产线体ID（外键）
    - `created_time`: 创建时间
  - 约束：`material_id` 和 `production_line_id` 的组合唯一

### 2. 后端API改进
- **新增API**: `/api/production-lines`
  - 功能：获取所有生产线体列表（用于dispatch页面筛选）
  
- **新增API**: `/api/requests/filtered`
  - 功能：获取筛选后的待发料请求
  - 参数：`production_line_id`（可选）
  - 筛选逻辑：根据物料的生产线体进行筛选
  
- **新增API**: `/api/materials-info`
  - 功能：获取所有物料信息
  
- **新增API**: `/api/init-sample-data`
  - 功能：初始化示例数据，建立物料与生产线体的关系

- **增强现有API**:
  - `/api/all-requests` 现在支持 `date` 参数进行日期筛选
  - `/api/requests/filtered` 现在支持 `date` 参数与生产线体筛选组合使用
  - `/api/requests/filtered` 现在返回所有状态的请求（包含已完成），而不仅仅是pending状态

- **新增测试API**: `/api/create-test-requests`
  - 功能：创建包含不同时间和状态的测试请求数据
  - 用于验证延迟显示和已完成请求显示功能

### 3. 前端界面改进
- **删除了返回叫料页面的链接**：按照要求移除了导航链接
- **添加右下角配置面板**：
  - 设计风格与request.html保持一致
  - 包含服务器地址配置
  - 包含生产线体筛选选择
  - **新增日期配置**：默认为今天，可选择其他日期
  - 配置按钮使用齿轮图标

- **添加筛选功能**：
  - 筛选信息显示区域（显示线体和日期筛选条件）
  - 清除筛选按钮
  - 实时筛选结果更新
  - 支持日期筛选功能

- **发料状态可视化**：
  - 已完成请求：文字显示删除线效果，透明度降低
  - 延迟请求：红色背景，红色文字，显示"DELAY"标识
  - 正常请求：保持原有样式

### 4. 功能特点
- **筛选逻辑**：
  - request页面的线体筛选：指哪个线体发起的需求
  - dispatch页面的线体筛选：指哪个线体生产的物料
  - **日期筛选**：两个页面都支持按日期筛选，默认显示今天的数据

- **用户体验**：
  - 右下角配置面板可以点击外部关闭
  - 筛选状态实时显示（包含线体和日期信息）
  - 日期选择器默认为今天
  - 清除筛选会重置日期为今天
  - 自动刷新功能保持不变
  - 响应式设计适配移动端

### 5. 测试支持
- 创建了 `test_dispatch.html` 测试页面
- 提供了完整的API测试功能
- 包含功能测试指南

## 使用说明

### 初始化数据
1. 启动应用程序：`python app.py`
2. 初始化示例数据：`POST http://localhost:8000/api/init-sample-data`

### 使用dispatch页面
1. 打开 `http://localhost:8000/dispatch.html`
2. 点击右下角齿轮按钮打开配置面板
3. 选择要筛选的生产线体（可选）
4. 选择查看日期（默认为今天）
5. 点击"应用"按钮
6. 页面将显示符合条件的待发料请求

### 使用request页面
1. 打开 `http://localhost:8000/request.html`
2. 点击右下角齿轮按钮打开配置面板
3. 选择生产线体（用于叫料和筛选）
4. 选择查看日期（默认为今天）
5. 点击"应用"按钮
6. 页面将显示符合条件的物料请求记录

### 筛选逻辑说明
- **生产线体筛选**：
  - dispatch页面：当选择"#1 MMLINE"时，只显示在#1线体生产的物料的发料请求
  - request页面：当选择"#1 MMLINE"时，只显示#1线体发起的物料请求
- **日期筛选**：只显示指定日期的请求记录
- **组合筛选**：可以同时使用生产线体和日期筛选
- 筛选信息会在页面顶部显示，可以点击"显示所有"清除筛选

## 技术实现

### 数据库查询
```sql
-- 筛选查询示例
SELECT mr.id, pl.line_name, m.material_id, m.material_description, 
       mr.quantity, m.base_unit, mr.request_time, mr.status
FROM maf_pulling.material_requests mr
JOIN maf_pulling.production_lines pl ON mr.line_id = pl.line_id
JOIN maf_pulling.materials m ON mr.material_id = m.material_id
WHERE mr.status = 'pending'
AND EXISTS (
    SELECT 1 FROM maf_pulling.material_production_lines mpl 
    WHERE mpl.material_id = m.material_id 
    AND mpl.production_line_id = ?
)
ORDER BY mr.request_time DESC
```

### 前端筛选流程
1. 页面加载时获取生产线体列表
2. 用户选择筛选条件
3. 调用筛选API获取数据
4. 更新页面显示和筛选信息
5. 提供清除筛选功能

## 文件修改清单
- `app.py`: 添加新API接口和数据库表创建
- `dispatch.html`: 重新设计界面，添加配置面板和筛选功能
- `test_dispatch.html`: 新建测试页面
- `DISPATCH_UPGRADE_SUMMARY.md`: 本文档

## 注意事项
- 新的数据库表需要手动维护物料与生产线体的关系
- 示例数据仅用于测试，生产环境需要根据实际情况配置
- 筛选功能基于物料的生产线体，而非请求的发起线体
