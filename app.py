from flask import Flask, render_template, request, jsonify, Response, stream_with_context
from flask_cors import CORS
import psycopg2
from psycopg2.extras import RealDictCursor
from datetime import datetime
import os
import requests
import base64
import json
import ssl

app = Flask(__name__)
CORS(app)  # 启用CORS支持

# AI API配置
AI_API_CONFIG = {
    'url': "http://*************:8013/v3/chat/completions",
    'api_key': "3e0d356f-fe37-bc9d-e101-672f6b19f29f",
    'headers': {
        "Content-Type": "application/json",
        "Authorization": "Bearer 3e0d356f-fe37-bc9d-e101-672f6b19f29f"
    }
}

# 数据库连接配置
DB_CONFIG = {
    'host': '*************',
    'port': 5432,
    'database': 'postgres',
    'user': 'postgres',
    'password': 'e82e0c77a0eb'
}

def get_db_connection():
    """获取数据库连接，并设置时区为北京时间"""
    conn = psycopg2.connect(**DB_CONFIG)
    # 设置连接的时区为北京时间
    cursor = conn.cursor()
    cursor.execute("SET timezone = 'Asia/Shanghai'")
    conn.commit()
    return conn

# 数据库初始化 - 检查连接（数据已手动插入）
def init_db():
    try:
        conn = get_db_connection()
        cursor = conn.cursor()

        # 显示连接信息
        cursor.execute("SELECT current_database(), current_user")
        db_info = cursor.fetchone()
        print(f"🔗 连接信息: 数据库={db_info[0]}, 用户={db_info[1]}")

        # 检查和显示时区设置
        cursor.execute("SHOW timezone")
        timezone_info = cursor.fetchone()
        print(f"🕐 数据库时区: {timezone_info[0]}")

        # 显示当前时间（北京时间）
        cursor.execute("SELECT CURRENT_TIMESTAMP AT TIME ZONE 'Asia/Shanghai' as beijing_time")
        beijing_time = cursor.fetchone()
        print(f"🕐 北京时间: {beijing_time[0]}")

        # 检查schema是否存在
        cursor.execute("SELECT schema_name FROM information_schema.schemata WHERE schema_name = 'maf_pulling'")
        schema_exists = cursor.fetchone()
        print(f"📁 Schema存在: {schema_exists is not None}")

        if schema_exists:
            # 创建物料生产线体表（如果不存在）
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS maf_pulling.material_production_lines (
                    id SERIAL PRIMARY KEY,
                    material_id VARCHAR(50) NOT NULL,
                    production_line_id INTEGER NOT NULL,
                    created_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (material_id) REFERENCES maf_pulling.materials(material_id),
                    FOREIGN KEY (production_line_id) REFERENCES maf_pulling.production_lines(line_id),
                    UNIQUE(material_id, production_line_id)
                )
            ''')

            # 检查数据库连接和数据
            cursor.execute("SELECT COUNT(*) FROM maf_pulling.production_lines")
            line_count = cursor.fetchone()[0]

            cursor.execute("SELECT COUNT(*) FROM maf_pulling.materials")
            material_count = cursor.fetchone()[0]

            cursor.execute("SELECT COUNT(*) FROM maf_pulling.line_materials")
            relation_count = cursor.fetchone()[0]

            # 检查新表
            cursor.execute("SELECT COUNT(*) FROM maf_pulling.material_production_lines")
            production_relation_count = cursor.fetchone()[0]

            print(f"✅ 数据库连接成功!")
            print(f"📊 数据统计: 生产线体({line_count}条), 物料({material_count}条), 线体物料关系({relation_count}条), 物料生产线体关系({production_relation_count}条)")

            if line_count > 0:
                cursor.execute("SELECT line_id, line_name FROM maf_pulling.production_lines LIMIT 3")
                lines = cursor.fetchall()
                print("📋 生产线体示例:")
                for line in lines:
                    print(f"  ID: {line[0]}, 名称: {line[1]}")

            # 提交事务
            conn.commit()
        else:
            print("❌ maf_pulling schema不存在")

        conn.close()

    except Exception as e:
        print(f"❌ 数据库连接失败: {e}")
        import traceback
        traceback.print_exc()

# 路由定义
@app.route('/test')
def test_route():
    return jsonify({'status': 'ok', 'message': 'Server is working'})

@app.route('/')
def admin_page():
    return render_template('admin.html')

@app.route('/admin')
def admin_page_alt():
    return render_template('admin.html')

# @app.route('/request')
# def request_page():
#     return render_template('request.html')
#
# @app.route('/dispatch')
# def dispatch_page():
#     return render_template('dispatch.html')

@app.route('/api/lines')
def get_lines():
    try:
        conn = get_db_connection()
        cursor = conn.cursor(cursor_factory=RealDictCursor)
        cursor.execute("SELECT line_id as id, line_name as name, description FROM maf_pulling.production_lines")
        lines = [dict(row) for row in cursor.fetchall()]
        conn.close()
        print(f"API /api/lines 返回 {len(lines)} 条记录")
        return jsonify(lines)
    except Exception as e:
        print(f"API /api/lines 错误: {e}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/materials/<int:line_id>')
def get_materials(line_id):
    conn = get_db_connection()
    cursor = conn.cursor(cursor_factory=RealDictCursor)
    cursor.execute('''
        SELECT m.material_id as id, m.material_id as code, m.material_description as name, m.base_unit as unit,
               COALESCE(m.minimum_order_quantity, 1) as min_order_qty
        FROM maf_pulling.materials m
        JOIN maf_pulling.line_materials lm ON m.material_id = lm.material_id
        WHERE lm.line_id = %s
    ''', (line_id,))
    materials = [dict(row) for row in cursor.fetchall()]
    conn.close()
    return jsonify(materials)

@app.route('/api/request', methods=['POST'])
def create_request():
    try:
        data = request.json
        print(f"API /api/request 接收到数据: {data}")

        if not data:
            return jsonify({'success': False, 'message': '请求数据为空'}), 400

        line_id = data.get('line_id')
        material_id = data.get('material_id')
        quantity = data.get('quantity')

        print(f"解析参数: line_id={line_id}, material_id={material_id}, quantity={quantity}")

        if not line_id:
            return jsonify({'success': False, 'message': '缺少生产线体ID'}), 400
        if not material_id:
            return jsonify({'success': False, 'message': '缺少物料ID'}), 400
        if not quantity:
            return jsonify({'success': False, 'message': '缺少数量'}), 400

        # 验证数据类型
        try:
            line_id = int(line_id)
            # material_id 保持为字符串，因为数据库中是字符串类型
            material_id = str(material_id)
            quantity = int(quantity)
        except (ValueError, TypeError):
            return jsonify({'success': False, 'message': '参数格式错误'}), 400

        if quantity <= 0:
            return jsonify({'success': False, 'message': '数量必须大于0'}), 400

        conn = get_db_connection()
        cursor = conn.cursor()
        cursor.execute('''
            INSERT INTO maf_pulling.material_requests (line_id, material_id, quantity, request_time)
            VALUES (%s, %s, %s, CURRENT_TIMESTAMP AT TIME ZONE 'Asia/Shanghai')
        ''', (line_id, material_id, quantity))
        conn.commit()
        conn.close()

        print(f"叫料请求创建成功: line_id={line_id}, material_id={material_id}, quantity={quantity}")
        return jsonify({'success': True, 'message': '叫料请求已提交'})

    except Exception as e:
        print(f"API /api/request 错误: {e}")
        import traceback
        traceback.print_exc()
        return jsonify({'success': False, 'message': f'服务器错误: {str(e)}'}), 500

@app.route('/api/requests')
def get_requests():
    try:
        print("API /api/requests 被调用")
        conn = get_db_connection()
        cursor = conn.cursor(cursor_factory=RealDictCursor)
        cursor.execute('''
            SELECT mr.id, pl.line_name as line_name, m.material_id as material_code, m.material_description as material_name,
                   mr.quantity, m.base_unit as unit,
                   (mr.request_time AT TIME ZONE 'Asia/Shanghai')::timestamp as request_time,
                   mr.status
            FROM maf_pulling.material_requests mr
            JOIN maf_pulling.production_lines pl ON mr.line_id = pl.line_id
            JOIN maf_pulling.materials m ON mr.material_id = m.material_id
            WHERE mr.status = 'pending'
            ORDER BY mr.request_time DESC
        ''')
        requests = [dict(row) for row in cursor.fetchall()]
        conn.close()
        print(f"API /api/requests 返回 {len(requests)} 条记录")
        return jsonify(requests)
    except Exception as e:
        print(f"API /api/requests 错误: {e}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/all-requests')
def get_all_requests():
    try:
        date_filter = request.args.get('date')
        print(f"API /api/all-requests 被调用，日期筛选: {date_filter}")

        conn = get_db_connection()
        cursor = conn.cursor(cursor_factory=RealDictCursor)

        base_query = '''
            SELECT mr.id, pl.line_name as line_name, m.material_id as material_code, m.material_description as material_name,
                   mr.quantity, m.base_unit as unit,
                   (mr.request_time AT TIME ZONE 'Asia/Shanghai')::timestamp as request_time,
                   mr.status,
                   (mr.dispatch_time AT TIME ZONE 'Asia/Shanghai')::timestamp as dispatch_time
            FROM maf_pulling.material_requests mr
            JOIN maf_pulling.production_lines pl ON mr.line_id = pl.line_id
            JOIN maf_pulling.materials m ON mr.material_id = m.material_id
        '''

        params = []
        if date_filter:
            base_query += ' WHERE DATE(mr.request_time) = %s'
            params.append(date_filter)

        base_query += ' ORDER BY mr.request_time DESC'

        cursor.execute(base_query, params)
        requests = [dict(row) for row in cursor.fetchall()]
        conn.close()
        print(f"API /api/all-requests 返回 {len(requests)} 条记录")
        return jsonify(requests)
    except Exception as e:
        print(f"API /api/all-requests 错误: {e}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/dispatch/<int:request_id>', methods=['POST'])
def dispatch_material(request_id):
    conn = get_db_connection()
    cursor = conn.cursor()
    cursor.execute('''
        UPDATE maf_pulling.material_requests
        SET status = 'completed', dispatch_time = CURRENT_TIMESTAMP AT TIME ZONE 'Asia/Shanghai'
        WHERE id = %s
    ''', (request_id,))
    conn.commit()
    conn.close()

    return jsonify({'success': True, 'message': '发料完成'})

@app.route('/api/production-lines')
def get_production_lines():
    """获取所有生产线体（用于dispatch页面筛选）"""
    try:
        conn = get_db_connection()
        cursor = conn.cursor(cursor_factory=RealDictCursor)
        cursor.execute("SELECT line_id as id, line_name as name, description FROM maf_pulling.production_lines ORDER BY line_name")
        lines = [dict(row) for row in cursor.fetchall()]
        conn.close()
        print(f"API /api/production-lines 返回 {len(lines)} 条记录")
        return jsonify(lines)
    except Exception as e:
        print(f"API /api/production-lines 错误: {e}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/requests/filtered')
def get_filtered_requests():
    """获取筛选后的发料请求（包含所有状态）"""
    try:
        production_line_id = request.args.get('production_line_id', type=int)
        date_filter = request.args.get('date')
        print(f"API /api/requests/filtered 被调用，筛选条件: production_line_id={production_line_id}, date={date_filter}")

        conn = get_db_connection()
        cursor = conn.cursor(cursor_factory=RealDictCursor)

        # 基础查询 - 移除status='pending'限制，显示所有状态
        base_query = '''
            SELECT mr.id, pl.line_name as line_name, m.material_id as material_code,
                   m.material_description as material_name, mr.quantity, m.base_unit as unit,
                   (mr.request_time AT TIME ZONE 'Asia/Shanghai')::timestamp as request_time,
                   mr.status,
                   (mr.dispatch_time AT TIME ZONE 'Asia/Shanghai')::timestamp as dispatch_time
            FROM maf_pulling.material_requests mr
            JOIN maf_pulling.production_lines pl ON mr.line_id = pl.line_id
            JOIN maf_pulling.materials m ON mr.material_id = m.material_id
            WHERE 1=1
        '''

        params = []

        # 如果指定了生产线体筛选，添加筛选条件
        if production_line_id:
            # 筛选条件：物料在指定生产线体生产
            base_query += '''
                AND EXISTS (
                    SELECT 1 FROM maf_pulling.material_production_lines mpl
                    WHERE mpl.material_id = m.material_id
                    AND mpl.production_line_id = %s
                )
            '''
            params.append(production_line_id)

        # 如果指定了日期筛选，添加日期条件
        if date_filter:
            base_query += ' AND DATE(mr.request_time) = %s'
            params.append(date_filter)

        base_query += ' ORDER BY mr.request_time DESC'

        cursor.execute(base_query, params)
        requests = [dict(row) for row in cursor.fetchall()]
        conn.close()

        print(f"API /api/requests/filtered 返回 {len(requests)} 条记录")
        return jsonify(requests)
    except Exception as e:
        print(f"API /api/requests/filtered 错误: {e}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/materials-info')
def get_materials_info():
    """获取所有物料信息"""
    try:
        conn = get_db_connection()
        cursor = conn.cursor(cursor_factory=RealDictCursor)
        cursor.execute("SELECT material_id, material_description FROM maf_pulling.materials")
        materials = [dict(row) for row in cursor.fetchall()]
        conn.close()
        return jsonify(materials)
    except Exception as e:
        print(f"API /api/materials-info 错误: {e}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/init-sample-data', methods=['POST'])
def init_sample_data():
    """初始化示例数据 - 为物料生产线体关系表添加数据"""
    try:
        conn = get_db_connection()
        cursor = conn.cursor()

        # 首先获取实际的物料ID
        cursor.execute("SELECT material_id FROM maf_pulling.materials")
        actual_materials = [row[0] for row in cursor.fetchall()]

        if len(actual_materials) < 3:
            return jsonify({'error': '数据库中物料数量不足，无法创建示例数据'}), 400

        # 清空现有数据
        cursor.execute("DELETE FROM maf_pulling.material_production_lines")

        # 使用实际的物料ID创建示例数据
        sample_data = [
            # 第一个物料可以在线体1和2生产
            (actual_materials[0], 1),
            (actual_materials[0], 2),
            # 第二个物料可以在线体2和3生产
            (actual_materials[1], 2),
            (actual_materials[1], 3),
            # 第三个物料可以在线体1和3生产
            (actual_materials[2], 1),
            (actual_materials[2], 3),
        ]

        for material_id, line_id in sample_data:
            cursor.execute('''
                INSERT INTO maf_pulling.material_production_lines (material_id, production_line_id)
                VALUES (%s, %s)
                ON CONFLICT (material_id, production_line_id) DO NOTHING
            ''', (material_id, line_id))

        conn.commit()
        conn.close()

        return jsonify({'success': True, 'message': '示例数据初始化完成', 'materials_used': actual_materials[:3]})
    except Exception as e:
        print(f"API /api/init-sample-data 错误: {e}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/create-test-requests', methods=['POST'])
def create_test_requests():
    """创建测试请求数据，包含延迟和已完成的请求"""
    try:
        conn = get_db_connection()
        cursor = conn.cursor()

        # 获取实际的物料和生产线体ID
        cursor.execute("SELECT material_id FROM maf_pulling.materials LIMIT 3")
        materials = [row[0] for row in cursor.fetchall()]

        cursor.execute("SELECT line_id FROM maf_pulling.production_lines LIMIT 3")
        lines = [row[0] for row in cursor.fetchall()]

        if len(materials) < 2 or len(lines) < 2:
            return jsonify({'error': '数据不足，无法创建测试数据'}), 400

        # 创建不同时间的测试请求
        test_requests = [
            # 2小时前的pending请求（应该显示为延迟）
            {
                'line_id': lines[0],
                'material_id': materials[0],
                'quantity': 100,
                'request_time': "CURRENT_TIMESTAMP - INTERVAL '2 hours'",
                'status': 'pending'
            },
            # 30分钟前的pending请求（正常）
            {
                'line_id': lines[1],
                'material_id': materials[1],
                'quantity': 200,
                'request_time': "CURRENT_TIMESTAMP - INTERVAL '30 minutes'",
                'status': 'pending'
            },
            # 3小时前的已完成请求（应该显示横线）
            {
                'line_id': lines[0],
                'material_id': materials[1],
                'quantity': 150,
                'request_time': "CURRENT_TIMESTAMP - INTERVAL '3 hours'",
                'status': 'completed',
                'dispatch_time': "CURRENT_TIMESTAMP - INTERVAL '2 hours 30 minutes'"
            },
            # 刚刚的pending请求（正常）
            {
                'line_id': lines[1],
                'material_id': materials[0],
                'quantity': 300,
                'request_time': "CURRENT_TIMESTAMP - INTERVAL '5 minutes'",
                'status': 'pending'
            }
        ]

        for req in test_requests:
            if req['status'] == 'completed':
                cursor.execute(f'''
                    INSERT INTO maf_pulling.material_requests (line_id, material_id, quantity, request_time, status, dispatch_time)
                    VALUES (%s, %s, %s, {req['request_time']}, %s, {req['dispatch_time']})
                ''', (req['line_id'], req['material_id'], req['quantity'], req['status']))
            else:
                cursor.execute(f'''
                    INSERT INTO maf_pulling.material_requests (line_id, material_id, quantity, request_time, status)
                    VALUES (%s, %s, %s, {req['request_time']}, %s)
                ''', (req['line_id'], req['material_id'], req['quantity'], req['status']))

        conn.commit()
        conn.close()

        return jsonify({'success': True, 'message': '测试请求数据创建完成', 'count': len(test_requests)})
    except Exception as e:
        print(f"API /api/create-test-requests 错误: {e}")
        return jsonify({'error': str(e)}), 500

# ==================== 主数据管理API ====================

@app.route('/api/admin/production-lines', methods=['GET'])
def admin_get_production_lines():
    """获取所有生产线体（管理页面用）"""
    try:
        conn = get_db_connection()
        cursor = conn.cursor(cursor_factory=RealDictCursor)
        cursor.execute("""
            SELECT line_id, line_name, description
            FROM maf_pulling.production_lines
            ORDER BY line_id
        """)
        lines = [dict(row) for row in cursor.fetchall()]
        conn.close()
        return jsonify(lines)
    except Exception as e:
        print(f"API /api/admin/production-lines GET 错误: {e}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/admin/production-lines', methods=['POST'])
def admin_create_production_line():
    """创建新的生产线体"""
    try:
        data = request.get_json()
        line_name = data.get('line_name', '').strip()
        description = data.get('description', '').strip()

        if not line_name:
            return jsonify({'error': '生产线体名称不能为空'}), 400

        conn = get_db_connection()
        cursor = conn.cursor()
        cursor.execute("""
            INSERT INTO maf_pulling.production_lines (line_name, description)
            VALUES (%s, %s)
            RETURNING line_id
        """, (line_name, description))

        line_id = cursor.fetchone()[0]
        conn.commit()
        conn.close()

        return jsonify({'success': True, 'message': '生产线体创建成功', 'line_id': line_id})
    except Exception as e:
        print(f"API /api/admin/production-lines POST 错误: {e}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/admin/production-lines/<int:line_id>', methods=['PUT'])
def admin_update_production_line(line_id):
    """更新生产线体信息"""
    try:
        data = request.get_json()
        line_name = data.get('line_name', '').strip()
        description = data.get('description', '').strip()

        if not line_name:
            return jsonify({'error': '生产线体名称不能为空'}), 400

        conn = get_db_connection()
        cursor = conn.cursor()
        cursor.execute("""
            UPDATE maf_pulling.production_lines
            SET line_name = %s, description = %s
            WHERE line_id = %s
        """, (line_name, description, line_id))

        if cursor.rowcount == 0:
            return jsonify({'error': '生产线体不存在'}), 404

        conn.commit()
        conn.close()

        return jsonify({'success': True, 'message': '生产线体更新成功'})
    except Exception as e:
        print(f"API /api/admin/production-lines PUT 错误: {e}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/admin/production-lines/<int:line_id>', methods=['DELETE'])
def admin_delete_production_line(line_id):
    """删除生产线体"""
    try:
        conn = get_db_connection()
        cursor = conn.cursor()

        # 检查是否有相关的物料请求
        cursor.execute("SELECT COUNT(*) FROM maf_pulling.material_requests WHERE line_id = %s", (line_id,))
        request_count = cursor.fetchone()[0]

        if request_count > 0:
            return jsonify({'error': f'无法删除，该生产线体有 {request_count} 个相关的物料请求'}), 400

        # 删除相关的线体物料关系
        cursor.execute("DELETE FROM maf_pulling.line_materials WHERE line_id = %s", (line_id,))
        cursor.execute("DELETE FROM maf_pulling.material_production_lines WHERE production_line_id = %s", (line_id,))

        # 删除生产线体
        cursor.execute("DELETE FROM maf_pulling.production_lines WHERE line_id = %s", (line_id,))

        if cursor.rowcount == 0:
            return jsonify({'error': '生产线体不存在'}), 404

        conn.commit()
        conn.close()

        return jsonify({'success': True, 'message': '生产线体删除成功'})
    except Exception as e:
        print(f"API /api/admin/production-lines DELETE 错误: {e}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/admin/materials', methods=['GET'])
def admin_get_materials():
    """获取所有物料（管理页面用）"""
    try:
        conn = get_db_connection()
        cursor = conn.cursor(cursor_factory=RealDictCursor)
        cursor.execute("""
            SELECT material_id, material_description, base_unit, minimum_order_quantity
            FROM maf_pulling.materials
            ORDER BY material_id
        """)
        materials = [dict(row) for row in cursor.fetchall()]
        conn.close()
        return jsonify(materials)
    except Exception as e:
        print(f"API /api/admin/materials GET 错误: {e}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/admin/materials', methods=['POST'])
def admin_create_material():
    """创建新的物料"""
    try:
        data = request.get_json()
        material_id = data.get('material_id', '').strip()
        material_description = data.get('material_description', '').strip()
        base_unit = data.get('base_unit', '').strip()
        minimum_order_quantity = data.get('minimum_order_quantity', 1)

        if not material_id or not material_description:
            return jsonify({'error': '物料编码和描述不能为空'}), 400

        conn = get_db_connection()
        cursor = conn.cursor()
        cursor.execute("""
            INSERT INTO maf_pulling.materials (material_id, material_description, base_unit, minimum_order_quantity)
            VALUES (%s, %s, %s, %s)
        """, (material_id, material_description, base_unit, minimum_order_quantity))

        conn.commit()
        conn.close()

        return jsonify({'success': True, 'message': '物料创建成功'})
    except Exception as e:
        if 'duplicate key' in str(e).lower():
            return jsonify({'error': '物料编码已存在'}), 400
        print(f"API /api/admin/materials POST 错误: {e}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/admin/materials/<material_id>', methods=['PUT'])
def admin_update_material(material_id):
    """更新物料信息"""
    try:
        data = request.get_json()
        material_description = data.get('material_description', '').strip()
        base_unit = data.get('base_unit', '').strip()
        minimum_order_quantity = data.get('minimum_order_quantity', 1)

        if not material_description:
            return jsonify({'error': '物料描述不能为空'}), 400

        conn = get_db_connection()
        cursor = conn.cursor()
        cursor.execute("""
            UPDATE maf_pulling.materials
            SET material_description = %s, base_unit = %s, minimum_order_quantity = %s
            WHERE material_id = %s
        """, (material_description, base_unit, minimum_order_quantity, material_id))

        if cursor.rowcount == 0:
            return jsonify({'error': '物料不存在'}), 404

        conn.commit()
        conn.close()

        return jsonify({'success': True, 'message': '物料更新成功'})
    except Exception as e:
        print(f"API /api/admin/materials PUT 错误: {e}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/admin/materials/<material_id>', methods=['DELETE'])
def admin_delete_material(material_id):
    """删除物料"""
    try:
        conn = get_db_connection()
        cursor = conn.cursor()

        # 检查是否有相关的物料请求
        cursor.execute("SELECT COUNT(*) FROM maf_pulling.material_requests WHERE material_id = %s", (material_id,))
        request_count = cursor.fetchone()[0]

        if request_count > 0:
            return jsonify({'error': f'无法删除，该物料有 {request_count} 个相关的物料请求'}), 400

        # 删除相关的关系数据
        cursor.execute("DELETE FROM maf_pulling.line_materials WHERE material_id = %s", (material_id,))
        cursor.execute("DELETE FROM maf_pulling.material_production_lines WHERE material_id = %s", (material_id,))

        # 删除物料
        cursor.execute("DELETE FROM maf_pulling.materials WHERE material_id = %s", (material_id,))

        if cursor.rowcount == 0:
            return jsonify({'error': '物料不存在'}), 404

        conn.commit()
        conn.close()

        return jsonify({'success': True, 'message': '物料删除成功'})
    except Exception as e:
        print(f"API /api/admin/materials DELETE 错误: {e}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/admin/line-materials', methods=['GET'])
def admin_get_line_materials():
    """获取线体物料关系"""
    try:
        conn = get_db_connection()
        cursor = conn.cursor(cursor_factory=RealDictCursor)
        cursor.execute("""
            SELECT lm.line_id, lm.material_id,
                   pl.line_name, m.material_description
            FROM maf_pulling.line_materials lm
            JOIN maf_pulling.production_lines pl ON lm.line_id = pl.line_id
            JOIN maf_pulling.materials m ON lm.material_id = m.material_id
            ORDER BY lm.line_id, lm.material_id
        """)
        relations = [dict(row) for row in cursor.fetchall()]
        conn.close()
        return jsonify(relations)
    except Exception as e:
        print(f"API /api/admin/line-materials GET 错误: {e}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/admin/line-materials', methods=['POST'])
def admin_create_line_material():
    """创建线体物料关系"""
    try:
        data = request.get_json()
        line_id = data.get('line_id')
        material_id = data.get('material_id', '').strip()

        if not line_id or not material_id:
            return jsonify({'error': '生产线体和物料不能为空'}), 400

        conn = get_db_connection()
        cursor = conn.cursor()
        cursor.execute("""
            INSERT INTO maf_pulling.line_materials (line_id, material_id)
            VALUES (%s, %s)
        """, (line_id, material_id))

        conn.commit()
        conn.close()

        return jsonify({'success': True, 'message': '线体物料关系创建成功'})
    except Exception as e:
        if 'duplicate key' in str(e).lower():
            return jsonify({'error': '该关系已存在'}), 400
        print(f"API /api/admin/line-materials POST 错误: {e}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/admin/line-materials/<int:line_id>/<material_id>', methods=['DELETE'])
def admin_delete_line_material(line_id, material_id):
    """删除线体物料关系"""
    try:
        conn = get_db_connection()
        cursor = conn.cursor()
        cursor.execute("DELETE FROM maf_pulling.line_materials WHERE line_id = %s AND material_id = %s", (line_id, material_id))

        if cursor.rowcount == 0:
            return jsonify({'error': '关系不存在'}), 404

        conn.commit()
        conn.close()

        return jsonify({'success': True, 'message': '线体物料关系删除成功'})
    except Exception as e:
        print(f"API /api/admin/line-materials DELETE 错误: {e}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/admin/material-production-lines', methods=['GET'])
def admin_get_material_production_lines():
    """获取物料生产线体关系"""
    try:
        conn = get_db_connection()
        cursor = conn.cursor(cursor_factory=RealDictCursor)
        cursor.execute("""
            SELECT mpl.id, mpl.material_id, mpl.production_line_id,
                   m.material_description, pl.line_name
            FROM maf_pulling.material_production_lines mpl
            JOIN maf_pulling.materials m ON mpl.material_id = m.material_id
            JOIN maf_pulling.production_lines pl ON mpl.production_line_id = pl.line_id
            ORDER BY mpl.material_id, mpl.production_line_id
        """)
        relations = [dict(row) for row in cursor.fetchall()]
        conn.close()
        return jsonify(relations)
    except Exception as e:
        print(f"API /api/admin/material-production-lines GET 错误: {e}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/admin/material-production-lines', methods=['POST'])
def admin_create_material_production_line():
    """创建物料生产线体关系"""
    try:
        data = request.get_json()
        material_id = data.get('material_id', '').strip()
        production_line_id = data.get('production_line_id')

        if not material_id or not production_line_id:
            return jsonify({'error': '物料和生产线体不能为空'}), 400

        conn = get_db_connection()
        cursor = conn.cursor()
        cursor.execute("""
            INSERT INTO maf_pulling.material_production_lines (material_id, production_line_id)
            VALUES (%s, %s)
        """, (material_id, production_line_id))

        conn.commit()
        conn.close()

        return jsonify({'success': True, 'message': '物料生产线体关系创建成功'})
    except Exception as e:
        if 'duplicate key' in str(e).lower():
            return jsonify({'error': '该关系已存在'}), 400
        print(f"API /api/admin/material-production-lines POST 错误: {e}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/admin/material-production-lines/<int:relation_id>', methods=['DELETE'])
def admin_delete_material_production_line(relation_id):
    """删除物料生产线体关系"""
    try:
        conn = get_db_connection()
        cursor = conn.cursor()
        cursor.execute("DELETE FROM maf_pulling.material_production_lines WHERE id = %s", (relation_id,))

        if cursor.rowcount == 0:
            return jsonify({'error': '关系不存在'}), 404

        conn.commit()
        conn.close()

        return jsonify({'success': True, 'message': '物料生产线体关系删除成功'})
    except Exception as e:
        print(f"API /api/admin/material-production-lines DELETE 错误: {e}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/admin/statistics', methods=['GET'])
def admin_get_statistics():
    """获取数据统计信息"""
    try:
        conn = get_db_connection()
        cursor = conn.cursor(cursor_factory=RealDictCursor)

        # 获取各表的统计信息
        stats = {}

        # 生产线体统计
        cursor.execute("SELECT COUNT(*) as count FROM maf_pulling.production_lines")
        stats['production_lines'] = cursor.fetchone()['count']

        # 物料统计
        cursor.execute("SELECT COUNT(*) as count FROM maf_pulling.materials")
        stats['materials'] = cursor.fetchone()['count']

        # 线体物料关系统计
        cursor.execute("SELECT COUNT(*) as count FROM maf_pulling.line_materials")
        stats['line_materials'] = cursor.fetchone()['count']

        # 物料生产线体关系统计
        cursor.execute("SELECT COUNT(*) as count FROM maf_pulling.material_production_lines")
        stats['material_production_lines'] = cursor.fetchone()['count']

        # 物料请求统计
        cursor.execute("""
            SELECT status, COUNT(*) as count
            FROM maf_pulling.material_requests
            GROUP BY status
        """)
        request_stats = {row['status']: row['count'] for row in cursor.fetchall()}
        stats['material_requests'] = request_stats

        # 今日请求统计
        cursor.execute("""
            SELECT COUNT(*) as count
            FROM maf_pulling.material_requests
            WHERE DATE(request_time AT TIME ZONE 'Asia/Shanghai') = CURRENT_DATE
        """)
        stats['today_requests'] = cursor.fetchone()['count']

        conn.close()
        return jsonify(stats)
    except Exception as e:
        print(f"API /api/admin/statistics GET 错误: {e}")
        return jsonify({'error': str(e)}), 500

# ==================== AI聊天API ====================

def encode_image_base64(image_data):
    """将图片数据编码为base64"""
    return base64.b64encode(image_data).decode('utf-8')

@app.route('/ai-chat')
def ai_chat_page():
    """AI聊天页面"""
    try:
        with open('ai_chat.html', 'r', encoding='utf-8') as f:
            return f.read()
    except FileNotFoundError:
        return "AI聊天页面未找到", 404

@app.route('/api/chat', methods=['POST'])
def chat_proxy():
    """AI聊天代理API，支持流式输出"""
    try:
        data = request.get_json()
        print(f"收到聊天请求: model={data.get('model')}, stream={data.get('stream', False)}")

        # 准备转发的数据
        forward_data = {
            "model": data.get('model', 'DeepSeek-V3'),
            "temperature": data.get('temperature', 0.7),
            "max_tokens": data.get('max_tokens', 2048),
            "messages": data.get('messages', []),
            "stream": data.get('stream', False)
        }

        # 发送请求到AI API
        response = requests.post(
            AI_API_CONFIG['url'],
            json=forward_data,
            headers=AI_API_CONFIG['headers'],
            verify=False,
            stream=forward_data['stream']
        )

        if not response.ok:
            print(f"AI API错误: {response.status_code} - {response.text}")
            try:
                error_data = response.json()
                if 'msg' in error_data and 'wrong api key' in error_data['msg']:
                    return jsonify({
                        'error': 'API密钥错误或已过期',
                        'message': '请联系**************获取有效的API密钥',
                        'details': error_data.get('msg', '')
                    }), 401
                else:
                    return jsonify({'error': f'AI API错误: {response.status_code}', 'details': error_data}), response.status_code
            except:
                return jsonify({'error': f'AI API错误: {response.status_code}', 'details': response.text}), response.status_code

        # 如果是流式响应
        if forward_data['stream']:
            def generate():
                try:
                    for line in response.iter_lines(decode_unicode=True):
                        if line:
                            if line.startswith('data: '):
                                yield line + '\n'
                            else:
                                yield f'data: {line}\n'
                    yield 'data: [DONE]\n'
                except Exception as e:
                    print(f"流式响应错误: {e}")
                    yield f'data: {{"error": "流式响应错误: {str(e)}"}}\n'

            return Response(
                stream_with_context(generate()),
                mimetype='text/plain',
                headers={
                    'Cache-Control': 'no-cache',
                    'Connection': 'keep-alive',
                    'Access-Control-Allow-Origin': '*',
                    'Access-Control-Allow-Headers': 'Content-Type',
                }
            )
        else:
            # 非流式响应
            return jsonify(response.json())

    except Exception as e:
        print(f"聊天代理错误: {e}")
        import traceback
        traceback.print_exc()
        return jsonify({'error': f'服务器错误: {str(e)}'}), 500

@app.route('/api/chat/models', methods=['GET'])
def get_available_models():
    """获取可用的AI模型列表"""
    models = [
        {
            'id': 'DeepSeek-V3',
            'name': 'DeepSeek-V3',
            'description': '深度求索V3模型，适合代码和推理任务',
            'supports_vision': False
        },
        {
            'id': 'Doubao-1.5-vision-pro',
            'name': 'Doubao-1.5-vision-pro',
            'description': '豆包视觉专业版，支持图像理解',
            'supports_vision': True
        },
        {
            'id': 'gpt-4',
            'name': 'GPT-4',
            'description': 'OpenAI GPT-4模型',
            'supports_vision': True
        },
        {
            'id': 'gpt-3.5-turbo',
            'name': 'GPT-3.5-Turbo',
            'description': 'OpenAI GPT-3.5-Turbo模型',
            'supports_vision': False
        }
    ]
    return jsonify(models)

if __name__ == '__main__':
    init_db()
    app.run(debug=True, host='0.0.0.0', port=8000)
