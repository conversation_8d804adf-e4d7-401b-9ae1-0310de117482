<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>物料叫料管理系统</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
            max-width: 1200px;
            margin: 0 auto;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 28px;
            margin-bottom: 10px;
        }

        .header p {
            font-size: 14px;
            opacity: 0.9;
        }
        
        .content {
            padding: 30px;
        }

        .requests-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }

        .requests-table th,
        .requests-table td {
            padding: 15px;
            text-align: left;
            border-bottom: 1px solid #e9ecef;
        }

        .requests-table th {
            background: #f8f9fa;
            font-weight: 600;
            color: #495057;
        }

        .requests-table tr:hover {
            background: #f8f9fa;
        }

        .empty-state {
            text-align: center;
            padding: 60px 20px;
            color: #6c757d;
        }

        .empty-state h3 {
            margin-bottom: 10px;
            font-size: 20px;
        }

        .empty-state p {
            font-size: 14px;
        }

        .loading {
            text-align: center;
            padding: 40px;
            color: #6c757d;
        }

        .filter-info {
            background: #e3f2fd;
            border: 1px solid #bbdefb;
            border-radius: 6px;
            padding: 12px 15px;
            margin-bottom: 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            color: #1565c0;
            font-size: 14px;
        }

        .clear-filter-btn {
            background: #2196f3;
            color: white;
            border: none;
            padding: 6px 12px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 12px;
            transition: background 0.3s;
        }

        .clear-filter-btn:hover {
            background: #1976d2;
        }
        
        .nav-link {
            color: #667eea;
            text-decoration: none;
            font-weight: 500;
        }

        .nav-link:hover {
            text-decoration: underline;
        }

        .controls {
            padding: 20px 30px;
            background: #f8f9fa;
            border-bottom: 1px solid #e9ecef;
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
            gap: 15px;
        }

        .material-controls {
            display: flex;
            align-items: center;
            gap: 15px;
            flex-wrap: wrap;
        }

        .control-group {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .control-group label {
            font-size: 14px;
            color: #495057;
            white-space: nowrap;
            font-weight: 500;
        }

        .control-group select,
        .control-group input {
            padding: 8px 12px;
            border: 1px solid #ced4da;
            border-radius: 4px;
            font-size: 14px;
            min-width: 150px;
        }

        .control-group select:focus,
        .control-group input:focus {
            outline: none;
            border-color: #667eea;
        }

        .control-group select:disabled,
        .control-group input:disabled {
            background-color: #e9ecef;
            cursor: not-allowed;
        }

        .quantity-hint {
            font-size: 12px;
            color: #6c757d;
            white-space: nowrap;
        }

        .control-buttons {
            display: flex;
            gap: 10px;
        }

        .refresh-btn,
        .request-btn {
            border: none;
            padding: 10px 20px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            transition: background 0.3s;
            font-weight: 500;
        }

        .refresh-btn {
            background: #28a745;
            color: white;
        }

        .refresh-btn:hover {
            background: #218838;
        }

        .request-btn {
            background: #667eea;
            color: white;
        }

        .request-btn:hover:not(:disabled) {
            background: #5a6fd8;
        }

        .request-btn:disabled {
            background: #6c757d;
            cursor: not-allowed;
        }
        
        .message {
            padding: 12px;
            border-radius: 6px;
            margin-bottom: 20px;
            text-align: center;
        }

        .message.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .message.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        .status-badge {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 500;
        }

        .status-pending {
            background: #fff3cd;
            color: #856404;
        }

        .status-completed {
            background: #d4edda;
            color: #155724;
        }

        /* 右下角配置角标 */
        .config-badge {
            position: fixed;
            bottom: 20px;
            right: 20px;
            z-index: 1000;
        }

        .config-toggle {
            background: #667eea;
            color: white;
            border: none;
            width: 60px;
            height: 60px;
            border-radius: 50%;
            cursor: pointer;
            font-size: 24px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            transition: all 0.3s;
        }

        .config-toggle:hover {
            background: #5a6fd8;
            transform: scale(1.1);
        }

        .config-panel {
            position: absolute;
            bottom: 70px;
            right: 0;
            background: white;
            border-radius: 10px;
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
            padding: 20px;
            width: 300px;
            display: none;
        }

        .config-panel.show {
            display: block;
        }

        .config-group {
            margin-bottom: 15px;
        }

        .config-group:last-child {
            margin-bottom: 0;
        }

        .config-group label {
            display: block;
            margin-bottom: 5px;
            font-size: 14px;
            color: #495057;
            font-weight: 500;
        }

        .config-group input,
        .config-group select {
            width: 100%;
            padding: 8px 12px;
            border: 1px solid #ced4da;
            border-radius: 4px;
            font-size: 14px;
        }

        .config-group input:focus,
        .config-group select:focus {
            outline: none;
            border-color: #667eea;
        }

        .config-actions {
            display: flex;
            gap: 10px;
            margin-top: 15px;
        }

        .config-btn {
            flex: 1;
            padding: 8px 16px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            transition: background 0.3s;
        }

        .config-btn.primary {
            background: #667eea;
            color: white;
        }

        .config-btn.primary:hover {
            background: #5a6fd8;
        }

        .config-btn.secondary {
            background: #6c757d;
            color: white;
        }

        .config-btn.secondary:hover {
            background: #5a6268;
        }

        @media (max-width: 768px) {
            .container {
                margin: 0;
                border-radius: 0;
            }

            .controls {
                flex-direction: column;
                gap: 15px;
            }

            .material-controls {
                flex-direction: column;
                align-items: stretch;
                gap: 10px;
            }

            .control-group {
                flex-direction: column;
                align-items: stretch;
                gap: 5px;
            }

            .control-group select,
            .control-group input {
                min-width: auto;
                width: 100%;
            }

            .requests-table {
                font-size: 12px;
            }

            .requests-table th,
            .requests-table td {
                padding: 10px 8px;
            }

            .config-panel {
                width: 280px;
                right: -10px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>物料叫料管理</h1>
            <p>查看已叫过的物料发料情况和详情</p>
        </div>

        <div class="controls">
            <div class="material-controls">
                <div class="control-group">
                    <label for="materialSelectHeader">选择物料：</label>
                    <select id="materialSelectHeader" disabled>
                        <option value="">请先在右下角选择生产线体</option>
                    </select>
                </div>
                <div class="control-group">
                    <label for="quantityHeader">所需数量：</label>
                    <input type="number" id="quantityHeader" min="1" step="1" placeholder="数量" disabled>
                    <span id="quantityHint" class="quantity-hint"></span>
                </div>
                <button class="request-btn" id="requestBtn" onclick="submitRequestFromHeader()" disabled>叫料</button>
            </div>
            <div class="control-buttons">
                <button class="refresh-btn" onclick="loadAllRequests()">刷新列表</button>
            </div>
        </div>

        <div class="content">
            <div id="message" class="message" style="display: none;"></div>

            <div id="filterInfo" class="filter-info" style="display: none;">
                <span id="filterText"></span>
                <button class="clear-filter-btn" onclick="clearFilter()">显示所有</button>
            </div>

            <div id="loading" class="loading">正在加载物料请求...</div>

            <div id="requestsContainer" style="display: none;">
                <table class="requests-table">
                    <thead>
                        <tr>
                            <th>请求ID</th>
                            <th>生产线体</th>
                            <th>物料编码</th>
                            <th>物料名称</th>
                            <th>数量</th>
                            <th>请求时间</th>
                            <th>发料时间</th>
                            <th>状态</th>
                        </tr>
                    </thead>
                    <tbody id="requestsTableBody">
                    </tbody>
                </table>
            </div>

            <div id="emptyState" class="empty-state" style="display: none;">
                <h3>暂无物料请求</h3>
                <p>当前没有任何物料请求记录</p>
            </div>
        </div>
    </div>

    <!-- 右下角配置角标 -->
    <div class="config-badge">
        <button class="config-toggle" onclick="toggleConfig()">⚙️</button>
        <div class="config-panel" id="configPanel">
            <div class="config-group">
                <label for="serverUrl">后端服务器地址：</label>
                <input type="text" id="serverUrl" value="http://localhost:8000" placeholder="http://localhost:8000">
            </div>
            <div class="config-group">
                <label for="lineSelect">选择生产线体：</label>
                <select id="lineSelect">
                    <option value="">请选择生产线体</option>
                </select>
            </div>
            <div class="config-group">
                <label for="dateSelect">查看日期：</label>
                <input type="date" id="dateSelect">
            </div>
            <div class="config-actions">
                <button class="config-btn primary" onclick="applyConfig()">应用</button>
                <button class="config-btn secondary" onclick="closeConfig()">关闭</button>
            </div>
        </div>
    </div>

    <script>
        let currentLineId = null;
        let currentMaterials = [];
        let currentDate = null;

        // 获取服务器地址
        function getServerUrl() {
            return document.getElementById('serverUrl').value.trim() || 'http://localhost:8000';
        }

        // 初始化日期为今天
        function initDate() {
            const today = new Date();
            const dateString = today.toISOString().split('T')[0];
            document.getElementById('dateSelect').value = dateString;
            currentDate = dateString;
        }

        // 页面加载时初始化
        document.addEventListener('DOMContentLoaded', function() {
            initDate();
            loadLines();
            loadAllRequests();
            // 每30秒自动刷新一次
            setInterval(loadAllRequests, 30000);
        });

        // 加载生产线体
        function loadLines() {
            const serverUrl = getServerUrl();
            fetch(`${serverUrl}/api/lines`)
                .then(response => {
                    if (!response.ok) {
                        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                    }
                    return response.json();
                })
                .then(lines => {
                    const lineSelect = document.getElementById('lineSelect');
                    lineSelect.innerHTML = '<option value="">请选择生产线体</option>';
                    lines.forEach(line => {
                        const option = document.createElement('option');
                        option.value = line.id;
                        option.textContent = `${line.name} - ${line.description}`;
                        lineSelect.appendChild(option);
                    });
                })
                .catch(error => {
                    console.error('加载生产线体失败:', error);
                    showMessage(`加载生产线体失败: ${error.message}`, 'error');
                });
        }
        
        // 加载所有物料请求
        function loadAllRequests() {
            document.getElementById('loading').style.display = 'block';
            document.getElementById('requestsContainer').style.display = 'none';
            document.getElementById('emptyState').style.display = 'none';

            const serverUrl = getServerUrl();
            let apiUrl = `${serverUrl}/api/all-requests`;

            // 如果有日期筛选，添加参数
            if (currentDate) {
                apiUrl += `?date=${currentDate}`;
            }

            fetch(apiUrl)
                .then(response => {
                    if (!response.ok) {
                        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                    }
                    return response.json();
                })
                .then(requests => {
                    document.getElementById('loading').style.display = 'none';
                    updateFilterInfo();

                    if (requests.length === 0) {
                        document.getElementById('emptyState').style.display = 'block';
                    } else {
                        displayRequests(requests);
                        document.getElementById('requestsContainer').style.display = 'block';
                    }
                })
                .catch(error => {
                    document.getElementById('loading').style.display = 'none';
                    console.error('加载物料请求失败:', error);
                    showMessage(`加载物料请求失败: ${error.message}`, 'error');
                });
        }

        // 显示请求列表
        function displayRequests(requests) {
            const tbody = document.getElementById('requestsTableBody');
            tbody.innerHTML = '';

            // 如果选择了生产线体，则进行筛选
            let filteredRequests = requests;
            if (currentLineId) {
                // 根据当前选择的生产线体筛选请求
                const currentLineName = getCurrentLineName();
                if (currentLineName) {
                    filteredRequests = requests.filter(request => request.line_name === currentLineName);
                }
            }

            filteredRequests.forEach(request => {
                const row = document.createElement('tr');
                row.innerHTML = `
                    <td>#${request.id}</td>
                    <td>${request.line_name}</td>
                    <td>${request.material_code}</td>
                    <td>${request.material_name}</td>
                    <td>${request.quantity} ${request.unit}</td>
                    <td>${formatDateTime(request.request_time)}</td>
                    <td>${request.dispatch_time ? formatDateTime(request.dispatch_time) : '-'}</td>
                    <td><span class="status-badge status-${request.status}">${getStatusText(request.status)}</span></td>
                `;
                tbody.appendChild(row);
            });

            // 如果筛选后没有数据，显示相应提示
            if (filteredRequests.length === 0 && requests.length > 0) {
                const row = document.createElement('tr');
                row.innerHTML = `
                    <td colspan="8" style="text-align: center; color: #6c757d; padding: 40px;">
                        当前生产线体暂无物料请求记录
                    </td>
                `;
                tbody.appendChild(row);
            }
        }

        // 获取当前选择的生产线体名称
        function getCurrentLineName() {
            if (!currentLineId) return null;

            const lineSelect = document.getElementById('lineSelect');
            const selectedOption = Array.from(lineSelect.options).find(option => option.value == currentLineId);
            return selectedOption ? selectedOption.textContent.split(' - ')[0] : null;
        }

        // 更新筛选信息显示
        function updateFilterInfo() {
            const filterInfo = document.getElementById('filterInfo');
            const filterText = document.getElementById('filterText');

            let filterParts = [];

            if (currentLineId) {
                const lineName = getCurrentLineName();
                if (lineName) {
                    filterParts.push(`线体：${lineName}`);
                }
            }

            if (currentDate) {
                const dateObj = new Date(currentDate);
                const dateString = dateObj.toLocaleDateString('zh-CN');
                filterParts.push(`日期：${dateString}`);
            }

            if (filterParts.length > 0) {
                filterText.textContent = `当前筛选：${filterParts.join('，')}`;
                filterInfo.style.display = 'flex';
            } else {
                filterInfo.style.display = 'none';
            }
        }

        // 清除筛选
        function clearFilter() {
            currentLineId = null;
            currentDate = null;

            // 重置配置面板中的生产线体选择
            document.getElementById('lineSelect').value = '';

            // 重置日期为今天
            initDate();

            // 清空物料选择
            const materialSelectHeader = document.getElementById('materialSelectHeader');
            materialSelectHeader.innerHTML = '<option value="">请先在右下角选择生产线体</option>';
            materialSelectHeader.disabled = true;

            // 禁用数量输入和叫料按钮
            document.getElementById('quantityHeader').disabled = true;
            document.getElementById('quantityHeader').value = '';
            document.getElementById('requestBtn').disabled = true;
            document.getElementById('quantityHint').textContent = '';

            // 更新筛选信息显示
            updateFilterInfo();

            // 重新加载请求列表以显示所有数据
            loadAllRequests();
        }

        // 应用配置（从右下角面板）
        function applyConfig() {
            const lineId = document.getElementById('lineSelect').value;
            const selectedDate = document.getElementById('dateSelect').value;

            // 更新当前日期
            currentDate = selectedDate;

            if (lineId) {
                currentLineId = lineId;
                loadMaterialsForLine(lineId);
                // 重新加载请求列表以应用筛选
                loadAllRequests();
                closeConfig();
            } else {
                // 如果没有选择生产线体，清除线体筛选但保留日期筛选
                currentLineId = null;
                // 清空物料选择
                const materialSelectHeader = document.getElementById('materialSelectHeader');
                materialSelectHeader.innerHTML = '<option value="">请先在右下角选择生产线体</option>';
                materialSelectHeader.disabled = true;
                // 禁用数量输入和叫料按钮
                document.getElementById('quantityHeader').disabled = true;
                document.getElementById('requestBtn').disabled = true;
                document.getElementById('quantityHint').textContent = '';
                // 重新加载请求列表以显示所有数据
                loadAllRequests();
                closeConfig();
            }
        }

        // 加载指定生产线体的物料
        function loadMaterialsForLine(lineId) {
            const serverUrl = getServerUrl();
            fetch(`${serverUrl}/api/materials/${lineId}`)
                .then(response => {
                    if (!response.ok) {
                        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                    }
                    return response.json();
                })
                .then(materials => {
                    currentMaterials = materials;
                    const materialSelectHeader = document.getElementById('materialSelectHeader');
                    materialSelectHeader.innerHTML = '<option value="">请选择物料</option>';
                    materials.forEach(material => {
                        const option = document.createElement('option');
                        option.value = material.id;
                        option.textContent = `${material.code} - ${material.name} (${material.unit})`;
                        option.dataset.minOrderQty = material.min_order_qty;
                        materialSelectHeader.appendChild(option);
                    });
                    materialSelectHeader.disabled = false;

                    // 启用数量输入和叫料按钮
                    document.getElementById('quantityHeader').disabled = false;
                    document.getElementById('requestBtn').disabled = false;
                })
                .catch(error => {
                    console.error('加载物料失败:', error);
                    showMessage(`加载物料失败: ${error.message}`, 'error');
                });
        }

        // 物料选择变化时更新数量提示
        document.getElementById('materialSelectHeader').addEventListener('change', function() {
            const selectedOption = this.options[this.selectedIndex];
            const quantityInput = document.getElementById('quantityHeader');
            const quantityHint = document.getElementById('quantityHint');

            if (selectedOption.value) {
                const minOrderQty = parseInt(selectedOption.dataset.minOrderQty) || 1;
                quantityInput.min = minOrderQty;
                quantityInput.step = minOrderQty;
                quantityInput.value = minOrderQty;
                quantityHint.textContent = `最小订购量: ${minOrderQty}`;
            } else {
                quantityInput.min = 1;
                quantityInput.step = 1;
                quantityInput.value = '';
                quantityHint.textContent = '';
            }
        });

        // 数量输入验证
        document.getElementById('quantityHeader').addEventListener('input', function() {
            const materialSelect = document.getElementById('materialSelectHeader');
            const selectedOption = materialSelect.options[materialSelect.selectedIndex];

            if (selectedOption.value) {
                const minOrderQty = parseInt(selectedOption.dataset.minOrderQty) || 1;
                const currentValue = parseInt(this.value);

                if (currentValue && currentValue % minOrderQty !== 0) {
                    const nearestMultiple = Math.ceil(currentValue / minOrderQty) * minOrderQty;
                    this.value = nearestMultiple;
                    showMessage(`数量已调整为最小订购量的倍数: ${nearestMultiple}`, 'success');
                }
            }
        });
        
        // 配置面板控制
        function toggleConfig() {
            const panel = document.getElementById('configPanel');
            panel.classList.toggle('show');
        }

        function closeConfig() {
            const panel = document.getElementById('configPanel');
            panel.classList.remove('show');
        }

        // 从表头提交叫料请求
        function submitRequestFromHeader() {
            const materialId = document.getElementById('materialSelectHeader').value;
            const quantity = document.getElementById('quantityHeader').value;

            if (!currentLineId || !materialId || !quantity) {
                showMessage('请填写完整信息', 'error');
                return;
            }

            const serverUrl = getServerUrl();
            fetch(`${serverUrl}/api/request`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    line_id: parseInt(currentLineId),
                    material_id: materialId,  // 保持为字符串
                    quantity: parseInt(quantity)
                })
            })
            .then(response => {
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                return response.json();
            })
            .then(data => {
                if (data.success) {
                    showMessage('叫料请求提交成功！', 'success');
                    // 清空表单
                    document.getElementById('materialSelectHeader').value = '';
                    document.getElementById('quantityHeader').value = '';
                    document.getElementById('quantityHint').textContent = '';
                    // 刷新列表
                    loadAllRequests();
                } else {
                    showMessage(data.message || '提交失败', 'error');
                }
            })
            .catch(error => {
                console.error('提交请求失败:', error);
                showMessage(`网络错误: ${error.message}`, 'error');
            });
        }
        
        // 格式化日期时间
        function formatDateTime(dateTimeStr) {
            const date = new Date(dateTimeStr);
            return date.toLocaleString('zh-CN', {
                year: 'numeric',
                month: '2-digit',
                day: '2-digit',
                hour: '2-digit',
                minute: '2-digit',
                timeZone: 'UTC' // 强制使用 UTC 时间
            });
        }

        // 获取状态文本
        function getStatusText(status) {
            const statusMap = {
                'pending': '待发料',
                'completed': '已完成'
            };
            return statusMap[status] || status;
        }

        // 显示消息
        function showMessage(text, type) {
            const messageDiv = document.getElementById('message');
            messageDiv.textContent = text;
            messageDiv.className = `message ${type}`;
            messageDiv.style.display = 'block';

            setTimeout(() => {
                messageDiv.style.display = 'none';
            }, 5000);
        }

        // 点击配置面板外部关闭
        document.addEventListener('click', function(event) {
            const configBadge = document.querySelector('.config-badge');
            const configPanel = document.getElementById('configPanel');

            if (!configBadge.contains(event.target) && configPanel.classList.contains('show')) {
                closeConfig();
            }
        });
    </script>
</body>
</html>
